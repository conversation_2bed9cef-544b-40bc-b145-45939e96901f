import { NextFunction, Request, Response } from 'express'

export const InstanceMiddleware = async (
  req: Request,
  res: Response,
  next: NextFunction
) => {
  const { instance } = req.body

  if (!instance) {
    return res.status(400).send({
      error: true,
      data: {
        message: 'instance are required',
      },
    })
  }

  const { id, accountId, platform } = instance
  if (!id || !accountId || !platform) {
    return res.status(400).send({
      error: true,
      data: {
        message: 'id, accountId and platform are required in instance',
      },
    })
  }

  next()
}
