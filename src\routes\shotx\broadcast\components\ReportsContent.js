import { <PERSON><PERSON>, Card, CardContent, Dialog, DialogActions, DialogContent, DialogContentText, DialogTitle, FormControl, Grid, InputLabel, makeStyles, MenuItem, Select, Snackbar, TextField } from "@material-ui/core";
import { Alert } from "@material-ui/lab";
import { langMessages } from "Lang/index";
import React, { useEffect, useState } from 'react';
import { getBroadcastLogsByContactId, getBroadcastsLastLogAddedByContactId } from "Repositories/broadcasts";
import { getLeadsByIds } from "Repositories/lead";
import { createSegmentation, getSegmentationsByAccountId, updateLeadsOfSegmentation } from "Repositories/segmentations";
import { getTaxonomiesLeadsTags } from "Repositories/taxionomies";
import LeadsTable from "Routes/components/LeadsTable";
import segmentationModel from "Routes/segmentations/model";
import { Loading } from "../../../../components/Widgets/components/Loading";
import { AppTriggersColors } from '../../../../constants/AppConstants';

export default function ReportsContent({ message }) {

    const type = message.instance.platform.toLowerCase()

    const broadcastStates = [
        { value: 'SENT', title: langMessages['shotx.broadcast.sent'] },
        ...(type !== 'instagram' ? [{ value: 'DELIVERED', title: langMessages['reports.delivered'] }] : []),
        { value: 'VIEWED', title: langMessages['reports.viewed'] },
        { value: 'REPLIED', title: langMessages['shotx.broadcast.replieds'] },
        { value: 'ERROR', title: langMessages['shotx.broadcast.failed'] }
    ];


    const useStyles = makeStyles((theme) => ({
        card: {
            width: "100%",
        },
        chartContainer: {
            width: "100%",
            height: "100%",
        },
    }));

    const classes = useStyles();
    const [contacts, setContacts] = React.useState([]);
    const [tags, setTags] = React.useState([]);
    const [loading, setLoading] = React.useState(false);
    const [targetSegmentation, setTargetSegmentation] = React.useState('');
    const [targetState, setTargetState] = React.useState('');
    const [filtredLeads, setFiltredLeads] = React.useState([])
    const [segmentations, setSegmentations] = React.useState([]);
    const [newSegmentationDialog, setNewSegmentationDialog] = React.useState(false);
    const [selectSegmentationDialog, setSelectSegmentationDialog] = React.useState(false);
    const [openSuccess, setOpenSuccess] = React.useState(false);
    const [newSegmentation, setNewSegmentation] = useState('');
    const tax = {
        tags: {
            leads: tags
        }
    }

    const getContacts = async () => {
        try {
            setLoading(true);

            const messageLeads = [...message.contacts];

            if (message?.segmentationsIds?.length) {
                messageLeads.push(...message.segmentationsIds);
            }

            const leads = await getLeadsByIds(messageLeads);

            const leadsData = await Promise.all(leads.map(async (lead) => {
                const [logs, lastLog] = await Promise.all([
                    getBroadcastLogsByContactId(message.id, lead.ID),
                    getBroadcastsLastLogAddedByContactId(message.id, lead.ID)
                ]);

                return {
                    ...lead,
                    logs,
                    log: lastLog[0] || null,
                    createdAt: lastLog[0]?.created || null
                };
            }));
            setContacts(leadsData);
            setFiltredLeads(leadsData);
        } catch (error) {
            console.error("Erro ao buscar contatos:", error);
        } finally {
            setLoading(false);
        }
    };

    const getSegmentations = () => {
        getSegmentationsByAccountId(message.instance.accountId).then((res) => {
            setSegmentations(res)
        })
    }

    const getTags = () => {
        getTaxonomiesLeadsTags(message.instance.accountId).then((res) => {
            setTags(res)
        })
    }

    const handleSelectSegmentation = (event) => {
        if (event) {
            setTargetSegmentation(event)
            setSelectSegmentationDialog(true)
        }
    }

    const handleSelectState = (event) => {

        if (event && event !== 'all') {
            const filteredContacts = contacts.filter(contact =>
                contact.logs.some(log => log.trigger.toLowerCase() === event.toLowerCase())
            );

            setTargetState(event);
            setFiltredLeads(filteredContacts);
        } else {
            setTargetState(event);
            setFiltredLeads(contacts);
        }
    };

    const handleOpenNewSegmentationDialog = () => {
        setNewSegmentationDialog(true)
    }

    const handleCloseSegmentationsDialog = () => {
        setNewSegmentation('')
        setNewSegmentationDialog(false)
        setSelectSegmentationDialog(false)
    }

    const handleCloseMessage = () => {
        setOpenSuccess(false)
    }

    const createNewSegmentation = () => {
        const { owner } = JSON.parse(localStorage.getItem('account'))
        const contactIds = filtredLeads.map(contact => contact.ID)

        const segmentation = {
            ...segmentationModel,
            title: newSegmentation,
            leads: contactIds,
            accountId: message.instance.accountId,
            owner: owner,
            config: {
                ...segmentationModel.config,
                dinamic: false,
            },
        }

        createSegmentation(segmentation).then(result => {
            if (result?.id) {
                setOpenSuccess(true)
            }
        })
        handleCloseSegmentationsDialog()
    }

    const AddLeadsToSegmentation = () => {
        const contactIds = filtredLeads.map(contact => contact.ID)
        const segmentation = segmentations.find(segmentation => segmentation.ID === targetSegmentation)
        updateLeadsOfSegmentation(segmentation, contactIds)
        handleCloseSegmentationsDialog()
    }

    useEffect(() => {
        setLoading(true)
        getContacts();
        getSegmentations();
        getTags();
    }, []);

    return (
        <>
            <Loading loading={loading} label={langMessages["texts.loading"]}>
                {
                    <Card className={classes.card}>
                        <CardContent className={classes.content}>
                            <Grid container alignItems="center" justifyContent="center">
                                <Grid item xs={12} md={12}>
                                    <LeadsTable
                                        page={1}
                                        fetching={false}
                                        FilterNavBar={
                                            <div className="d-flex justify-content-between align-items-center">
                                                <div className="flex-1 mx-5 text-right">
                                                    <Button
                                                        color="primary"
                                                        onClick={e => {
                                                            handleOpenNewSegmentationDialog()
                                                        }}
                                                    >
                                                        <i className="icon-plus mr-5"></i>
                                                        {langMessages['segmentations.create']}
                                                    </Button>
                                                </div>
                                                <div className="flex-1">
                                                    <FormControl className="w-100">
                                                        <InputLabel className="mx-2" id="form-select-segmentations">{langMessages['segmentations.title']}</InputLabel>
                                                        <Select
                                                            labelId="form-select-segmentations"
                                                            id="select-segmentations"
                                                            value={targetSegmentation}
                                                            onChange={(e) => handleSelectSegmentation(e.target.value)}
                                                            variant="outlined"
                                                        >
                                                            {
                                                                segmentations && segmentations.map((segmentation) => {
                                                                    return <MenuItem value={segmentation.ID} > {segmentation.title}</MenuItem>
                                                                })
                                                            }
                                                        </Select>
                                                    </FormControl>
                                                </div>
                                                <div className="flex-1">
                                                    <FormControl className="w-100 mx-2">
                                                        <InputLabel className="mx-2" id="form-select-status">{langMessages['widgets.status']}</InputLabel>
                                                        <Select
                                                            labelId="form-select-status"
                                                            id="select-status"
                                                            value={targetState}
                                                            onChange={(e) => handleSelectState(e.target.value)}
                                                            variant="outlined"
                                                        >
                                                            <MenuItem value={'all'} > {langMessages['widgets.all']} </MenuItem>
                                                            {
                                                                broadcastStates && broadcastStates.map((broadcastState) => {
                                                                    return <MenuItem value={broadcastState.value} > {broadcastState.title}</MenuItem>
                                                                })
                                                            }
                                                        </Select>
                                                    </FormControl>
                                                </div>
                                            </div>
                                        }
                                        recipients={filtredLeads}
                                        taxonomies={tax}
                                        eventColors={AppTriggersColors}
                                        from="broadcast"
                                    />

                                </Grid>
                            </Grid>
                        </CardContent>
                    </Card>
                }
            </Loading >

            <Dialog open={newSegmentationDialog} onClose={handleCloseSegmentationsDialog} aria-labelledby="form-dialog-title">
                <DialogTitle id="form-dialog-title">{langMessages['segmentations.add.new']}</DialogTitle>
                <DialogContent>
                    <DialogContentText>
                        {langMessages['segmentations.add.new.text']}
                    </DialogContentText>
                    <TextField
                        autoFocus
                        value={newSegmentation}
                        margin="dense"
                        id="segmentation-name"
                        label={langMessages['segmentations.add.name']}
                        type="text"
                        fullWidth
                        onChange={(e) => {
                            setNewSegmentation(e.target.value)
                        }}
                    />
                </DialogContent>
                <DialogActions>
                    <Button onClick={handleCloseSegmentationsDialog} color="primary">
                        {langMessages['button.back']}
                    </Button>
                    <Button onClick={createNewSegmentation} color="primary" variant="contained" disabled={!newSegmentation}>
                        {langMessages['button.confirm']}
                    </Button>
                </DialogActions>
            </Dialog>

            <Snackbar
                anchorOrigin={{ vertical: 'top', horizontal: 'right' }}
                open={openSuccess}
                onClose={handleCloseMessage}
                variant="success"
                autoHideDuration={2000}
            >
                <Alert severity="success" onClose={handleCloseMessage}>
                    {langMessages['leads.addedToSegmentation']}
                </Alert>
            </Snackbar>

            <Dialog open={selectSegmentationDialog} onClose={handleCloseSegmentationsDialog} aria-labelledby="form-dialog-title">
                <DialogContent>
                    <DialogContentText>
                        {langMessages['segmentations.add.leads']}
                    </DialogContentText>
                </DialogContent>
                <DialogActions>
                    <Button onClick={handleCloseSegmentationsDialog} color="primary">
                        {langMessages['button.back']}
                    </Button>
                    <Button onClick={AddLeadsToSegmentation} color="primary" variant="contained">
                        {langMessages['button.confirm']}
                    </Button>
                </DialogActions>
            </Dialog>
        </>

    )
}
