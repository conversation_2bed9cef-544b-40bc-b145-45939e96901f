import { firestore } from '../../../config'
import { deleteK<PERSON> } from '../../../libs/redis/redisClient'

export async function removeMessage(message: any, messageKey: string) {
  console.log('MESSAGE', message), console.log('MESSAGEKEY', messageKey)

  const ref = firestore
    .collection('shotx-cron')
    .doc(message.appointment_id)
    .collection('fails')
    .doc()

  const docToSave = {
    id: message.id,
    ...message,
  }
  await ref.set(docToSave, { merge: true })
  await deleteKey(message.redis_key)
}
