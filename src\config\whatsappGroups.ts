/**
 * WhatsApp Groups Configuration
 *
 * Configuration settings for WhatsApp group management functionality
 */

export const WhatsAppGroupsConfig = {
  // Maximum number of leads per group (WhatsApp limitation)
  MAX_LEADS_PER_GROUP: 500,

  // Maximum total leads that can be processed
  MAX_TOTAL_LEADS: 5000,

  // Delay between group creations to avoid rate limiting (milliseconds)
  GROUP_CREATION_DELAY: 2000,

  // Whether to use mock service (only when explicitly enabled)
  USE_MOCK_IN_DEVELOPMENT: false,

  // Evolution API endpoints for group management
  EVOLUTION_ENDPOINTS: {
    CREATE_GROUP: '/group/create',
    ADD_PARTICIPANTS: '/group/participants/add',
    GET_GROUP_INFO: '/group/info',
    LIST_GROUPS: '/group/list',
    REMOVE_PARTICIPANTS: '/group/participants/remove',
    UPDATE_GROUP_INFO: '/group/updateGroupInfo',
    LEAVE_GROUP: '/group/leave',
  },

  // Mock service configuration
  MOCK_CONFIG: {
    // Simulated API delay range (min, max in milliseconds)
    DELAY_RANGE: [1000, 3000],

    // Failure rate for testing error handling (0.0 to 1.0)
    FAILURE_RATE: 0.1,

    // Enable detailed logging for mock operations
    ENABLE_LOGGING: true,
  },

  // Group naming configuration
  NAMING: {
    // Default prefix if none provided
    DEFAULT_PREFIX: 'Grupo',

    // Maximum group name length
    MAX_NAME_LENGTH: 25,

    // Pattern for group numbering
    NUMBERING_PATTERN: '{prefix} {number}',
  },

  // Validation rules
  VALIDATION: {
    // Minimum number of participants required to create a group
    MIN_PARTICIPANTS: 1,

    // Maximum description length
    MAX_DESCRIPTION_LENGTH: 512,

    // Phone number validation pattern
    PHONE_PATTERN: /^\d{10,15}$/,
  },

  // Error messages
  ERROR_MESSAGES: {
    INVALID_INSTANCE: 'Invalid WhatsApp instance ID',
    INVALID_ACCOUNT: 'Invalid account ID',
    INVALID_USER: 'Invalid user ID',
    INVALID_GROUP_NAME: 'Invalid group name',
    INVALID_PARTICIPANTS: 'Invalid participants list',
    TOO_MANY_LEADS: `Maximum ${5000} leads allowed`,
    TOO_MANY_PARTICIPANTS: `Maximum ${500} participants per group`,
    GROUP_CREATION_FAILED: 'Failed to create WhatsApp group',
    EVOLUTION_API_ERROR: 'Evolution API error',
    RATE_LIMIT_EXCEEDED: 'Rate limit exceeded, please try again later',
  },

  // Success messages
  SUCCESS_MESSAGES: {
    GROUP_CREATED: 'Group created successfully',
    PARTICIPANTS_ADDED: 'Participants added successfully',
    GROUPS_CREATED: 'Multiple groups created successfully',
  },
}

/**
 * Get configuration based on environment
 */
export function getGroupsConfig() {
  const isDevelopment = process.env.NODE_ENV === 'development'
  const hasEvolutionAPI = !!process.env.WHATSAPP_API_URL
  const forceMock = process.env.USE_WHATSAPP_MOCK === 'true'

  return {
    ...WhatsAppGroupsConfig,
    useMockService: forceMock || (!hasEvolutionAPI && WhatsAppGroupsConfig.USE_MOCK_IN_DEVELOPMENT),
    hasRealAPI: hasEvolutionAPI,
    environment: isDevelopment ? 'development' : 'production',
    mockReason: forceMock ? 'explicitly_enabled' : !hasEvolutionAPI ? 'no_api_url' : 'disabled',
  }
}

/**
 * Validate group creation request
 */
export function validateGroupCreationRequest(request: {
  instanceId?: string
  accountId?: string
  userId?: string
  groupName?: string
  participants?: string[]
}) {
  const errors: string[] = []
  const config = WhatsAppGroupsConfig

  if (!request.instanceId?.trim()) {
    errors.push(config.ERROR_MESSAGES.INVALID_INSTANCE)
  }

  if (!request.accountId?.trim()) {
    errors.push(config.ERROR_MESSAGES.INVALID_ACCOUNT)
  }

  if (!request.userId?.trim()) {
    errors.push(config.ERROR_MESSAGES.INVALID_USER)
  }

  if (!request.groupName?.trim()) {
    errors.push(config.ERROR_MESSAGES.INVALID_GROUP_NAME)
  }

  if (!Array.isArray(request.participants) || request.participants.length === 0) {
    errors.push(config.ERROR_MESSAGES.INVALID_PARTICIPANTS)
  }

  if (request.participants && request.participants.length > config.MAX_LEADS_PER_GROUP) {
    errors.push(config.ERROR_MESSAGES.TOO_MANY_PARTICIPANTS)
  }

  return {
    isValid: errors.length === 0,
    errors
  }
}

/**
 * Validate multiple groups creation request
 */
export function validateMultipleGroupsRequest(request: {
  instanceId?: string
  accountId?: string
  userId?: string
  groupPrefix?: string
  leads?: any[]
}) {
  const errors: string[] = []
  const config = WhatsAppGroupsConfig

  if (!request.instanceId?.trim()) {
    errors.push(config.ERROR_MESSAGES.INVALID_INSTANCE)
  }

  if (!request.accountId?.trim()) {
    errors.push(config.ERROR_MESSAGES.INVALID_ACCOUNT)
  }

  if (!request.userId?.trim()) {
    errors.push(config.ERROR_MESSAGES.INVALID_USER)
  }

  if (!request.groupPrefix?.trim()) {
    errors.push('Group prefix is required')
  }

  if (!Array.isArray(request.leads) || request.leads.length === 0) {
    errors.push('Leads array is required and cannot be empty')
  }

  if (request.leads && request.leads.length > config.MAX_TOTAL_LEADS) {
    errors.push(config.ERROR_MESSAGES.TOO_MANY_LEADS)
  }

  return {
    isValid: errors.length === 0,
    errors
  }
}

export default WhatsAppGroupsConfig
