import { NextFunction, Request, Response } from 'express'
import { Config } from '../../../constants/config'
import LeadController from '../../../controllers/lead'
import { MessageUpSert } from '../../../libs/types/evolution'
import { getPhoneNumberInfo } from '../../../utils/phone.utils'

const sendErrorResponse = (
  res: Response,
  message: string,
  statusCode: number = 400,
  debug = ''
) => {
  console.warn(message, debug)
  return res.status(statusCode).send({
    error: true,
    message,
  })
}

export const WhatsappValidationsMiddleware = async (
  req: Request,
  res: Response,
  next: NextFunction
) => {
  try {
    const body: MessageUpSert = req.body
    // const data = body.data || {};
    const remoteJid = body.data?.remoteJid || body.data?.key?.remoteJid
    // console.log(
    //   `🚀 ~ 💬 WHATSAPP WEBHOOK EVENT '${body.event}':STATUS'${body.data?.status}'`,
    //   body
    // )

    // Ignorar notificações de entrega
    // if (body.data?.status === 'DELIVERY_ACK') {
    //   console.info(`IGNORING DELIVERY NOTIFICATION from ${remoteJid}`);
    //   return res.status(204).send(); // No Content
    // }

    if (!body?.event) {
      return sendErrorResponse(res, 'Unknow event type')
    }

    // Dividir a instância para extrair o accountId e instanceId
    const [accountId, instanceId] = body.instance.split(
      Config.instanceNameSeparator
    )

    if (!accountId) {
      return sendErrorResponse(
        res,
        'accountId not found in the request payload'
      )
    }

    if (!instanceId) {
      return sendErrorResponse(
        res,
        'instanceId not found in the request payload'
      )
    }

    // Processar e obter informações sobre o número de telefone
    if (remoteJid) {
      // Ignorar broadcast status
      if (remoteJid === 'status@broadcast') {
        console.info(`IGNORING STATUS@BROADCAST from ${remoteJid}`)
        return res.status(204).send()
      }
      // Ignorar mensagens de grupo
      if (remoteJid.includes('@g.') || body.data?.isGroup) {
        console.info(`IGNORING GROUP MESSAGE from ${remoteJid}`)
        return res.status(204).send()
      }

      const phone = remoteJid.replace('@', ':').split(':')[0]
      const phoneInfo = getPhoneNumberInfo(phone, true)

      if (phoneInfo) {
        const { national, international, countryCode } = phoneInfo

        // Inserir os dados processados no corpo da requisição
        req.body.phone = national
        req.body.phoneCC = countryCode
        req.body.phoneNumber = international
        // console.log('BODY', req.body)
        // Atualizar o remoteJid com o número corrigido (9º dígito)
        if (req.body.data?.key?.remoteJid) {
          req.body.data.key.remoteJid = `${international}@${remoteJid.split('@')[1]}`
        }
        if (req.body.data?.remoteJid) {
          req.body.data.remoteJid = `${international}@${remoteJid.split('@')[1]}`
        }

        // Busca o lead existente
        const lead = await LeadController.findByMobile(
          accountId,
          national,
          countryCode
        )
        if (lead) {
          console.log('🙋🏽 LEAD ENCONTRADO', {
            id: lead.id,
            name: lead.displayName,
          })
          req.body.lead = lead
          req.body.leadId = lead?.id
        }

        // Log informativo com os detalhes do número de telefone e instância
        console.log(
          `🚀 ~ 💬 CONTACT '${countryCode}${national}' FROM ACCOUNT '${accountId}' AND INSTANCE '${instanceId}'`
        )
      }
    }

    if (!body.message) {
      req.body.message =
        body.data?.message?.conversation ||
        body.data.message?.extendedTextMessage?.text ||
        ''
    }

    // Inserir os dados processados no corpo da requisição
    req.body.accountId = accountId
    req.body.instanceId = instanceId
    req.body.instanceName = body.instance
    req.body.instanceEvoId = body.instance
    req.body.isEdited = body.data?.messageType == 'editedMessage'
    req.body.fromMe = body.data?.fromMe || body.data?.key?.fromMe || false
    req.body.messageId = body.data?.messageId || body.data?.key?.id

    // console.log(`🚀 ~ 💬 BODY`, messageId, JSON.stringify(body));
    // Continuar para o próximo middleware ou rota
    next()
  } catch (error: any) {
    console.error(`Error in WhatsappWebhookMiddleware: ${error.message}`, {
      body: req.body,
    })
    res.status(500).send({
      error: true,
      message: 'Internal Server Error',
    })
  }
}
