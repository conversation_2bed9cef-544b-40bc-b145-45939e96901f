import { logger } from './logger'
import { isDefined } from './replaceUndefined'

export function isEmptyObject(obj: any) {
  return (
    Boolean(obj) &&
    !Array.isArray(obj) &&
    typeof obj === 'object' &&
    !Object.keys(obj).length
  )
}
export function isEmptyValuesObject(
  obj: any,
  acceptNull: boolean,
  acceptEmpty: boolean,
  acceptZero: boolean,
  acceptNaN: boolean
): boolean {
  return (
    !isLoopable(obj) ||
    !Object.keys(obj).some((k) =>
      isLoopable(obj[k])
        ? !isEmptyValuesObject(
          obj[k],
          acceptNull,
          acceptEmpty,
          acceptZero,
          acceptNaN
        )
        : isDefined(obj[k], acceptNull, acceptEmpty, acceptZero, acceptNaN)
    )
  )
}
export function isLoopableObject(obj: any) {
  return (
    Boolean(obj) &&
    !Array.isArray(obj) &&
    typeof obj === 'object' &&
    Boolean(Object.keys(obj)) &&
    Boolean(Object.keys(obj).length)
  )
}
export function isLoopable(val: any) {
  return Array.isArray(val) || isLoopableObject(val)
}
export const isCyclic = (obj: any) => {
  const seenObjects: any = []
  function detect(obj: any, level: number) {
    level = level || 0
    let key
    if (isLoopable(obj)) {
      const keys = Object.keys(obj)
      const seenObjectIndex = seenObjects.indexOf(obj)
      if (seenObjectIndex > -1 && level > 1) {
        for (let k = 0; k < keys.length; k++) {
          key = keys[k]
          if (isLoopable(obj[key])) {
            logger.log('cycle in index ' + seenObjectIndex, {
              seenObjects,
              obj,
            })
            return true
          }
        }
      }
      seenObjects.push(obj)
      for (let k = 0; k < keys.length; k++) {
        key = keys[k]
        if (detect(obj[key], level + 1)) {
          logger.log('cycle at ' + key, obj)
          return true
        }
      }
    }
    return false
  }
  return detect(obj, 0)
}

type DiffResult = {
  [key: string]: {
    obj1: any
    obj2: any
  }
}

export function findDifference(
  oldObject: Record<string, any>,
  newObject: Record<string, any>,
): DiffResult {
  const differenceObj: any = {}

  Object.entries(oldObject).forEach(([key, value]) => {
    if (!!newObject[key]) { // Existe a chave no novo objeto
      if (typeof value === 'object') {
        const diff = findDifference(value, newObject[key])
        if (Object.keys(diff).length) {
          differenceObj[key] = diff
        }
      } else
        if (value !== newObject[key]) { // Os valores são diferentes
          differenceObj[key] = newObject[key]
        }
    } else {
      differenceObj[key] = value
    }
  })

  return differenceObj
}

/**
 * Merge obj2 in obj1 keeping the obj1 values and insert/update values from obj2
 * @param obj1 Base objeto to merged
 * @param obj2 Object to merge into obj1
 * @returns Object with data from obj1 and obj2
 */
export function mergeObject<T>(
  obj1: any,
  obj2: any,
): T {
  for (const [key, value] of Object.entries(obj2)) {
    if (typeof value == 'object') {
      obj1[key] = mergeObject(value, obj2[key])
    } else {
      obj1[key] = value || null
    }
  }
  return obj1
}
