import React from 'react'
import {
  Card,
  CardContent,
  Typography,
  Grid,
  Box,
  Chip
} from '@material-ui/core'
import { langMessages } from 'Lang/index'

const GroupStats = ({ 
  totalLeads, 
  selectedLeads, 
  groupsToCreate, 
  maxLeadsPerGroup,
  createdGroups = [],
  isCreating = false 
}) => {
  const calculateStats = () => {
    const stats = {
      totalSelected: selectedLeads,
      groupsNeeded: groupsToCreate,
      averageLeadsPerGroup: groupsToCreate > 0 ? Math.round(selectedLeads / groupsToCreate) : 0,
      remainingLeads: selectedLeads % maxLeadsPerGroup,
      createdCount: createdGroups.length,
      successRate: createdGroups.length > 0 ? Math.round((createdGroups.length / groupsToCreate) * 100) : 0
    }
    return stats
  }

  const stats = calculateStats()

  return (
    <Grid container spacing={2}>
      <Grid item xs={12} sm={6} md={3}>
        <Card>
          <CardContent>
            <Typography color="textSecondary" gutterBottom>
              {langMessages['whatsapp.groups.selectLeads.selected']}
            </Typography>
            <Typography variant="h4" component="h2">
              {stats.totalSelected}
            </Typography>
            <Typography color="textSecondary">
              de {totalLeads} disponíveis
            </Typography>
          </CardContent>
        </Card>
      </Grid>

      <Grid item xs={12} sm={6} md={3}>
        <Card>
          <CardContent>
            <Typography color="textSecondary" gutterBottom>
              Grupos Necessários
            </Typography>
            <Typography variant="h4" component="h2">
              {stats.groupsNeeded}
            </Typography>
            <Typography color="textSecondary">
              máx. {maxLeadsPerGroup} leads/grupo
            </Typography>
          </CardContent>
        </Card>
      </Grid>

      <Grid item xs={12} sm={6} md={3}>
        <Card>
          <CardContent>
            <Typography color="textSecondary" gutterBottom>
              Média por Grupo
            </Typography>
            <Typography variant="h4" component="h2">
              {stats.averageLeadsPerGroup}
            </Typography>
            <Typography color="textSecondary">
              leads por grupo
            </Typography>
          </CardContent>
        </Card>
      </Grid>

      <Grid item xs={12} sm={6} md={3}>
        <Card>
          <CardContent>
            <Typography color="textSecondary" gutterBottom>
              Status
            </Typography>
            <Box display="flex" alignItems="center">
              {isCreating ? (
                <Chip label="Criando..." color="primary" />
              ) : createdGroups.length > 0 ? (
                <Chip 
                  label={`${stats.createdCount} criados`} 
                  color="primary" 
                />
              ) : (
                <Chip label="Aguardando" color="default" />
              )}
            </Box>
            {createdGroups.length > 0 && (
              <Typography color="textSecondary" style={{ marginTop: 8 }}>
                {stats.successRate}% sucesso
              </Typography>
            )}
          </CardContent>
        </Card>
      </Grid>

      {stats.remainingLeads > 0 && (
        <Grid item xs={12}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Distribuição dos Leads
              </Typography>
              <Typography variant="body2" color="textSecondary">
                {groupsToCreate - 1} grupos terão {maxLeadsPerGroup} leads cada, 
                e 1 grupo terá {stats.remainingLeads} leads.
              </Typography>
            </CardContent>
          </Card>
        </Grid>
      )}
    </Grid>
  )
}

export default GroupStats
