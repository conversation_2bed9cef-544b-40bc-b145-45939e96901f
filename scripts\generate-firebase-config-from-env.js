const fs = require('fs');
const path = require('path');
const dotenv = require('dotenv');

// Load environment variables from .env file
console.log('Loading environment variables from .env file...');
const envPath = path.resolve(process.cwd(), '.env');
if (fs.existsSync(envPath)) {
  const result = dotenv.config({ path: envPath });
  if (result.error) {
    console.error('Error loading .env file:', result.error);
    process.exit(1);
  }
  console.log('Environment variables loaded successfully.');
} else {
  console.error('Error: .env file not found at', envPath);
  process.exit(1);
}

console.log('Iniciando geração do arquivo .account/qiplus-50eee-firebase-adminsdk-ofo94-8c76f01742.json');
console.log('Variáveis de ambiente disponíveis:', Object.keys(process.env).filter(key => key.startsWith('DEPLOY_FIREBASE_')));

// Função para gerar o arquivo .account/qiplus-50eee-firebase-adminsdk-ofo94-8c76f01742.json a partir de variáveis de ambiente
function generateFirebaseServiceAccount() {
  // Verifica se as variáveis de ambiente necessárias estão definidas
  const requiredEnvVars = [
    'DEPLOY_FIREBASE_PROJECT_ID',
    'DEPLOY_FIREBASE_PRIVATE_KEY',
    'DEPLOY_FIREBASE_CLIENT_EMAIL'
  ];

  const missingVars = requiredEnvVars.filter(varName => !process.env[varName]);
  if (missingVars.length > 0) {
    console.error(`Erro: Variáveis de ambiente ausentes: ${missingVars.join(', ')}`);
    process.exit(1);
  }

  try {
    // Obter a chave privada da variável de ambiente
    let privateKey = process.env.DEPLOY_FIREBASE_PRIVATE_KEY;

    console.log('Processando a chave privada...');

    // Verificar se a chave já está no formato correto
    if (!privateKey.startsWith('-----BEGIN PRIVATE KEY-----')) {
      console.error('Erro: A chave privada não está no formato PEM correto');
      process.exit(1);
    }

    // Processar a chave privada para garantir que as quebras de linha estejam corretas
    // Isso é especialmente importante em ambientes de produção como Coolify
    privateKey = privateKey
      .replace(/\\\\n/g, '\\n') // Corrige escape duplo que pode ocorrer em alguns ambientes
      .replace(/\\n/g, '\n');   // Converte \n em quebras de linha reais para o objeto JSON

    console.log('Chave privada processada com sucesso');

    // Criar o objeto de configuração do Firebase
    const serviceAccount = {
      type: 'service_account',
      project_id: process.env.DEPLOY_FIREBASE_PROJECT_ID,
      private_key_id: process.env.DEPLOY_FIREBASE_PRIVATE_KEY_ID || '',
      private_key: privateKey,
      client_email: process.env.DEPLOY_FIREBASE_CLIENT_EMAIL,
      client_id: process.env.DEPLOY_FIREBASE_CLIENT_ID || '',
      auth_uri: process.env.DEPLOY_FIREBASE_AUTH_URI || 'https://accounts.google.com/o/oauth2/auth',
      token_uri: process.env.DEPLOY_FIREBASE_TOKEN_URI || 'https://oauth2.googleapis.com/token',
      auth_provider_x509_cert_url: process.env.DEPLOY_FIREBASE_AUTH_PROVIDER_CERT_URL || 'https://www.googleapis.com/oauth2/v1/certs',
      client_x509_cert_url: process.env.DEPLOY_FIREBASE_CLIENT_CERT_URL || '',
      universe_domain: process.env.DEPLOY_FIREBASE_UNIVERSE_DOMAIN || 'googleapis.com'
    };

    // Escrever o arquivo
    fs.writeFileSync('.account/qiplus-50eee-firebase-adminsdk-ofo94-8c76f01742.json', JSON.stringify(serviceAccount, null, 2));
    console.log('Arquivo .account/qiplus-50eee-firebase-adminsdk-ofo94-8c76f01742.json gerado com sucesso!');

    // Log de verificação
    console.log('Conteúdo do arquivo gerado (sem a chave privada):');
    const logSafeServiceAccount = { ...serviceAccount };
    logSafeServiceAccount.private_key = '[REDACTED]';
    console.log(JSON.stringify(logSafeServiceAccount, null, 2));

    // Verificar se o arquivo foi criado corretamente
    if (fs.existsSync('.account/qiplus-50eee-firebase-adminsdk-ofo94-8c76f01742.json')) {
      try {
        const fileContent = fs.readFileSync('.account/qiplus-50eee-firebase-adminsdk-ofo94-8c76f01742.json', 'utf8');
        const parsedContent = JSON.parse(fileContent);
        console.log('Verificação do arquivo: Arquivo pode ser lido e parseado com sucesso.');

        if (parsedContent.private_key &&
            parsedContent.private_key.includes('-----BEGIN PRIVATE KEY-----') &&
            parsedContent.private_key.includes('-----END PRIVATE KEY-----')) {
          console.log('Verificação do arquivo: Chave privada parece estar no formato correto.');
        } else {
          console.error('Verificação do arquivo: Chave privada pode não estar no formato correto!');
        }
      } catch (parseError) {
        console.error('Erro ao analisar o arquivo JSON:', parseError);
      }
    } else {
      console.error('Verificação do arquivo: Arquivo não foi criado!');
    }
  } catch (error) {
    console.error('Erro ao escrever o arquivo .account/qiplus-50eee-firebase-adminsdk-ofo94-8c76f01742.json:', error);
    process.exit(1);
  }
}

// Executa a função
generateFirebaseServiceAccount();