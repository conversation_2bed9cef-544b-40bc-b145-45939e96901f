import moment from 'moment'
import { firestore } from '../../../config'
import { FIRESTORE_COLLECTIONS } from '../../../constants/collections'
import {
  SniperSessionStatusEnum,
  SniperSessionStatusTypes,
} from '../../../enums/sniper/SessionStatus'
import { SessionRef } from '../types/sniper.session'

export class SniperSessionRepository {
  getSessionRef(accountId: string, instanceId: string) {
    return firestore
      .collection(FIRESTORE_COLLECTIONS.SHOTX)
      .doc(accountId)
      .collection(FIRESTORE_COLLECTIONS.INSTANCES)
      .doc(instanceId)
      .collection(FIRESTORE_COLLECTIONS.SESSIONS)
  }

  getByContactId = async (
    instanceId: string,
    accountId: string,
    contactId: string
  ): Promise<SessionRef[]> => {
    const statuses = [
      SniperSessionStatusEnum.OPENED,
      SniperSessionStatusEnum.PAUSED,
    ]
    const sessions = this.getSessionRef(accountId, instanceId)
      .where('contactId', '==', contactId)
      .where('status', 'in', statuses)

    return await sessions
      .get()
      .then((snp) => snp.docs.map((doc) => doc.data() as SessionRef))
  }

  getAllByContactId = async (
    instanceId: string,
    accountId: string,
    contactId: string
  ): Promise<SessionRef[]> => {
    const sessions = this.getSessionRef(accountId, instanceId).where(
      'contactId',
      '==',
      contactId
    )

    return await sessions
      .get()
      .then((snp) => snp.docs.map((doc) => doc.data() as SessionRef))
  }

  getAllByContactIdAndOpened = async (
    instanceId: string,
    accountId: string,
    contactId: string,
    sessionId: string
  ): Promise<any[]> => {
    const sessions = this.getSessionRef(accountId, instanceId)
      .where('contactId', '==', contactId)
      .where('status', '==', 'opened')
      .where('id', '!=', sessionId)

    return await sessions
      .get()
      .then((snp) => snp.docs.map((doc) => doc.data() as SessionRef))
  }


  getAllContactSessionsOpened = async (
    instanceId: string,
    accountId: string,
    contactId: string
  ): Promise<any[]> => {
    const sessions = this.getSessionRef(accountId, instanceId)
      .where('contactId', '==', contactId)
      .where('status', '==', 'opened')

    return await sessions
      .get()
      .then((snp) => snp.docs.map((doc) => doc.data() as SessionRef))
  }

  create = async (session: SessionRef) => {
    const { accountId, instanceId } = session

    session.startedAt = moment().unix()

    return this.getSessionRef(accountId, instanceId)
      .add(session)
      .then((res) => res.set({ id: res.id }, { merge: true }))
  }

  close = async (session: SessionRef) => {
    const { accountId, instanceId, id } = session

    const status = SniperSessionStatusEnum.CLOSED
    const closedAt = moment().unix()

    return this.getSessionRef(accountId, instanceId)
      .doc(id)
      .set({ status, closedAt }, { merge: true })
  }

  update = async (session: SessionRef, status: SniperSessionStatusTypes) => {
    session.status = status
    const { accountId, instanceId, id } = session

    if (status == SniperSessionStatusEnum.CLOSED) {
      session.closedAt = moment().unix()
    }

    return this.getSessionRef(accountId, instanceId)
      .doc(id)
      .set(session, { merge: true })
  }
}
