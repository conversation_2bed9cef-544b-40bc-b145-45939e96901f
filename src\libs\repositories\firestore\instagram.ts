import { firestore } from '../../../config'
import { FIRESTORE_COLLECTIONS } from '../../../constants/collections'
import { AccountRepository } from './account'
import { InstancesRepository } from './instance'

export class InstagramRepository {
  private collectionName = FIRESTORE_COLLECTIONS.SHOTX

  constructor(
    private readonly instances = InstancesRepository,
    private readonly accounts = AccountRepository
  ) { }

  public saveEvent = async (
    accountId: string,
    instanceId: string,
    eventDoc: any,
    eventId: string,
    update = false
  ) => {
    try {
      const eventRef = this.getEventDocReference(instanceId, accountId, eventDoc.senderId, eventId)
      await eventRef.set(eventDoc, { merge: update })
    } catch (error) {
      console.log('EventDoc', eventDoc)
      console.error('Error saving event', error)
    }
  }

  public getEventDocReference = (
    instanceId: string,
    accountId: string,
    ig_sid: string,
    eventId: string
  ) => {
    const eventDocRef = firestore
      .collection(FIRESTORE_COLLECTIONS.SHOTX)
      .doc(accountId)
      .collection(FIRESTORE_COLLECTIONS.INSTANCES)
      .doc(instanceId)
      .collection(FIRESTORE_COLLECTIONS.CONTACTS)
      .doc(ig_sid)
      .collection(FIRESTORE_COLLECTIONS.EVENTS)
      .doc(eventId)

    return eventDocRef
  }

  saveMessage = async (
    accountId: string,
    instanceId: string,
    senderId: string,
    messageId: string,
    messageDoc: any,
    update = false
  ) => {
    try {
      const messageRef = this.getMessageDocReference(
        accountId,
        instanceId,
        senderId,
        messageId
      )
      console.log('messageDoc', accountId, instanceId, senderId, messageId, messageDoc)
      await messageRef.set(messageDoc, { merge: update })
    } catch (error) {
      console.log('MessageDoc', messageDoc)
      console.error('Error saving message', error)
    }
  }

  getLastFromMe = async (
    accountId: string,
    instanceId: string,
    senderId: string
  ): Promise<any | null> => {
    const instagramDocRef = await firestore
      .collection(this.collectionName)
      .doc(accountId)
      .collection(FIRESTORE_COLLECTIONS.INSTANCES)
      .doc(instanceId)
      .collection(FIRESTORE_COLLECTIONS.CONTACTS)
      .doc(senderId)
      .collection(FIRESTORE_COLLECTIONS.MESSAGES)
      .where('sender', '==', true)
      .orderBy('date', 'desc')
      .limit(1)
      .get()

    if (instagramDocRef.empty) {
      return null
    }

    let instagramDocs: any = [];

    instagramDocRef.forEach(doc => {
      instagramDocs.push(doc.data())
    });

    return instagramDocs[0]
  }

  updateMessage = async (
    accountId: string,
    instanceId: string,
    senderId: string,
    messageId: string,
    messageDoc: any
  ) => this.saveMessage(
    accountId,
    instanceId,
    senderId,
    messageId,
    messageDoc,
    true
  )

  getMessageDocReference = (
    accountId: string,
    instanceId: string,
    senderId: string,
    messageId: string
  ) => {
    const messageDocRef = firestore
      .collection(FIRESTORE_COLLECTIONS.SHOTX)
      .doc(accountId)
      .collection(FIRESTORE_COLLECTIONS.INSTANCES)
      .doc(instanceId)
      .collection(FIRESTORE_COLLECTIONS.CONTACTS)
      .doc(senderId)
      .collection(FIRESTORE_COLLECTIONS.MESSAGES)
      .doc(messageId)

    return messageDocRef
  }

}
