import { NextFunction, Request, Response } from 'express';
import { getPhoneNumberInfo } from '../../../utils/phone.utils';

export const ContactIdParamRequired = async (
  req: Request,
  res: Response,
  next: NextFunction
) => {
  try {
    console.log(`🚀 ~ 💬 WHATSAPP BROADCAST `, req.params);

    const { contactId } = req.params

    // Processar e obter informações sobre o número de telefone
    if (contactId) {

      const phoneInfo = getPhoneNumberInfo(contactId, true);

      if (phoneInfo) {

        const { national, international, countryCode } = phoneInfo;

        // Inserir os dados processados no corpo da requisição
        req.params.phone = national;
        req.params.phoneCC = countryCode;
        req.params.phoneNumber = international;

        // Atualizar o contactId com o número corrigido (9º dígito)
        req.params.contactid = international
      }
    }

    console.log(`🚀 ~ 💬 PARAMS`, JSON.stringify(req.params));
    // Continuar para o próximo middleware ou rota
    next();
  } catch (error: any) {
    console.error(`Error in ContactIdParamRequired: ${error.message}`, { body: req.params });
    res.status(500).send({
      error: true,
      message: 'Internal Server Error',
    });
  }
};
