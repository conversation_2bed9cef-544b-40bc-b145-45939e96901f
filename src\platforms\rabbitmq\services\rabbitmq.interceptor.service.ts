import { rabbitService } from '../../../app';
import { RabbitMQLogger } from '../helpers/rabbitmq.logger';
import { RabbitMQMessage } from '../types/rabbitmq.message';

export const RabbitMQIntercept = async <T extends string | object>(message: RabbitMQMessage<T>) => {
  try {
    const { data, queueName, onQueue, onReceive } = message

    const sended = await rabbitService.queueMessage<T>(queueName, data)

    RabbitMQLogger.log(`🚀 ~ 🐇⏳ RABBIT MQ: New message on queue '${queueName}'`);
    onQueue(sended)

    rabbitService.listenQueue<T>(queueName, (data) => {
      RabbitMQLogger.log(`🚀 ~ 🐇🔊 RABBIT MQ: Message from queue '${queueName}'`);
      onReceive(data)

      return true; // Sinaliza para o rabbitMQ que a mensagem foi recebida
    })

  } catch (error: any) {
    RabbitMQLogger.log(`🚀 ~ 🐇❌ RABBIT MQ: Failed to queue message:`, error);
  }
};