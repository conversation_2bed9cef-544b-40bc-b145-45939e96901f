import { Socket } from 'socket.io'
import { authenticationError } from '../../utils/authError'
import { decodeJwt } from '../../utils/jwtUtils'

export const SocketAuthMiddleware = async (socket: Socket, next: any) => {
  const { query } = socket.handshake
  const { token } = query

  if (!token) {
    console.log('🗝️ Socket Token required')
    return next(new Error('Token required'))
  }

  return decodeJwt(String(token))
    .then((decodedUser) => {
      if (decodedUser?.uid) {
        console.log('🗝️ Socket Valid Token')
        ;(socket as any).user = decodedUser
        return next()
      }
      console.log('🗝️ Socket Invalid Token')
      return next(new Error('Invalid Token'))
    })

    .catch((error) => next(new Error(authenticationError(error))))
}
