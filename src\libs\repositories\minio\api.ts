import * as Minio from 'minio'
import { sanitizeFileName } from '../../../utils/file'
import { MinioFile } from '../../types/minio/file'

const {
  S3_ACCESS_KEY = '',
  S3_SECRET_KEY = '',
  S3_BUCKET = '',
  S3_PORT = 443,
  S3_ENDPOINT = '',
  S3_USE_SSL = true
} = process.env

export class MinioRepository {
  private client: Minio.Client
  constructor() {
    this.client = new Minio.Client({
      endPoint: S3_ENDPOINT,
      port: Number(S3_PORT),
      useSSL: Bo<PERSON>an(S3_USE_SSL),
      accessKey: S3_ACCESS_KEY,
      secretKey: S3_SECRET_KEY,
    })
  }

  async uploadFile(
    instanceId: string,
    accountId: string,
    contactId: string,
    type: string,
    file: MinioFile,
  ) {

    const { originalname, buffer } = file

    const sanitizedName = sanitizeFileName(originalname)
    const fileName = `${accountId}/${instanceId}/${contactId}/${type}/${sanitizedName}`

    if (!await this.client.bucketExists(S3_BUCKET)) {
      await this.createBucketPublic()
      console.log('Bucket ' + S3_BUCKET + ' created!')
    }

    await this.client.putObject(S3_BUCKET, fileName, buffer)

    return `http${S3_USE_SSL ? 's' : ''}://${S3_ENDPOINT}:${S3_PORT}/${S3_BUCKET}/${accountId}/${instanceId}/${contactId}/${type}/${sanitizedName}`
  }

  async deleteFile(fileUrl: string) {
    const filePath = fileUrl.replace('//', '').split('/').slice(1).join('/')

    this.client.removeObject(S3_BUCKET, filePath)
  }

  private async createBucketPublic() {
    // const now = new Date();
    // const date = dateToString(now, 'YYYY-MM-DD')
    await this.client.makeBucket(S3_BUCKET)
    // await this.client.setBucketPolicy(S3_BUCKET, JSON.stringify({
    //   Version: date,
    //   Statement: [{
    //     Effect: "Allow",
    //     Principal: "*",
    //     Action: ["s3:GetObject"],
    //     Resource: ["arn:aws:s3:::" + S3_BUCKET + "/*"]
    //   }]
    // }));
  }
}