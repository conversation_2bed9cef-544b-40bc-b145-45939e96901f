import { config } from 'dotenv'
import admin from 'firebase-admin'
import { ENV_MODE } from './enums'

// Config dotenv
config()

// Config env
const isDev = process.env.NODE_ENV === ENV_MODE.DEVELOPMENT

const serviceAccountFile = process.env.CREDENTIALS_FILE
const FB_APP_ID = process.env.APP_ID
const firebaseConfig = {
  apiKey: process.env.API_KEY, // Your Api key will be here
  authDomain: `${FB_APP_ID}.firebaseapp.com`, // Your auth domain
  databaseURL: `https://${FB_APP_ID}.firebaseio.com`, // data base url
  projectId: `${FB_APP_ID}`, // project id
  storageBucket: `${FB_APP_ID}.appspot.com`, // storage bucket
  messagingSenderId: isDev ? '************' : '************', // messaging sender id
  appId: isDev ? undefined : '1:************:web:6ebfc0c6a2da6b9011e2a5',
  measurementId: isDev ? undefined : 'G-M0YX8B7N7K',
}

// URL for cloud functions
const cloudfunctionsBaseURL = `https://us-central1-${FB_APP_ID}.cloudfunctions.net`

// eslint-disable-next-line @typescript-eslint/no-var-requires
const cert = require(`../.account/${serviceAccountFile}`)

admin.initializeApp({
  ...firebaseConfig,
  credential: admin.credential.cert(cert),
})

const firestore = admin.firestore()

const auth = admin.auth()

export { FB_APP_ID, auth, cloudfunctionsBaseURL, firestore }
