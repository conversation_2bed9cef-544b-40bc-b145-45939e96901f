import moment from 'moment'
import { firestore } from '../../../config'
import { FIRESTORE_COLLECTIONS } from '../../../constants/collections'

export const saveShotxCronLogs = async (
  shotxCronId: string,
  accountId: string,
  instanceId: string,
  contactId: string,
  trigger: string,
  messageId?: string,
  payload?: any
) => {
  console.log(
    'INSIDE SAVE',
    'cronId',
    shotxCronId,
    'account',
    accountId,
    'instance',
    instanceId,
    'contact',
    contactId,
    'trigger',
    trigger,
    'Message',
    messageId,
    'LOAD',
    payload
  )
  const logsRef = firestore
    .collection(FIRESTORE_COLLECTIONS.SHOTXCRON)
    .doc(shotxCronId)
  const infoLog = {
    instanceId,
    accountId,
    shotxCronId,
    contactId,
    payload: payload || null,
    messageId: messageId || null,
    trigger,
    createdAt: moment().unix(),
  }
  const log = await logsRef.collection(FIRESTORE_COLLECTIONS.LOGS).add(infoLog)
}
