import { FirestoreRef } from 'FirebaseRef';

export const getBroadcastLogsByContactId = async (messageId, contactId) => {

    return await FirestoreRef.collection('shotx-cron')
        .doc(messageId).collection('logs')
        .where('contactId', '==', `${contactId}`)
        .orderBy('createdAt', 'asc')
        .get().then(snapshot => {
            const logs = []
            snapshot.forEach(doc => {
                logs.push(doc.data())
            })
            return logs
        })
}

export const getBroadcastsLastLogAddedByContactId = async (messageId, contactId) => {
    return await FirestoreRef.collection('shotx-cron')
        .doc(messageId).collection('logs')
        .where('contactId', '==', `${contactId}`)
        .orderBy('createdAt', 'desc')
        .limit(1)
        .get().then(snapshot => {
            const logs = []
            snapshot.forEach(doc => {
                logs.push(doc.data())
            })
            return logs
        })
}
