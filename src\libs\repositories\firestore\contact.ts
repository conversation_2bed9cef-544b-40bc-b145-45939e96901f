import { firestore } from '../../../config'
import { FIRESTORE_COLLECTIONS } from '../../../constants/collections'
import { replaceUndefined } from '../../../utils/replaceUndefined'
import { InstagramContactType } from '../../types/instance/contact'

export class ContactRepository {

  constructor(

  ) { }


  public getDocReference = (docRef: DocRefType) => {
    const { accountId, instanceId, contact: { ig_sid } } = docRef
    const whatsappDocRef = firestore
      .collection(FIRESTORE_COLLECTIONS.SHOTX)
      .doc(accountId)
      .collection(FIRESTORE_COLLECTIONS.INSTANCES)
      .doc(instanceId)
      .collection(FIRESTORE_COLLECTIONS.CONTACTS)
      .doc(ig_sid)

    return whatsappDocRef
  }

  get = async (docRef: DocRefType) => {
    // console.log('GETING CONTACT', { docRef })

    const response = {
      error: false,
      message: '',
      data: null as InstagramContactType | null,
    }

    try {
      const contactRef = this.getDocReference(docRef)

      const contactDoc = await contactRef.get()
      response.data = contactDoc.data() as InstagramContactType

      return response

    } catch (error: any) {
      console.error('Error getting contact', error)
      response.error = true
      response.message = error.message
      return response
    }
  }


  save = async (docRef: DocRefType) => {
    const { contact } = docRef
    const data = replaceUndefined(contact)
    try {
      await this.getDocReference(docRef).set(data, { merge: true })
    } catch (error) {
      console.error('Error saving contact', { contact, error })
    }
  }

  update = this.save
}

type DocRefType = {
  accountId: string,
  instanceId: string,
  contact: InstagramContactType
}