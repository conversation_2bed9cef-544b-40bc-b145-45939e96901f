type VideoMessageType = {
  keyId: string
  keyRemoteJid: string
  keyFromMe: boolean
  pushName: string
  content: {
    url: string;
    mimetype: string;
    fileSha256: string;
    fileLength: string;
    seconds: number;
    mediaKey: string;
    height: number;
    width: number;
    fileEncSha256: string;
    directPath: string;
    mediaKeyTimestamp: string;
    jpegThumbnail: string;
    streamingSidecar: string;
    contextInfo: ObjectConstructor[]
  }
  isGroup: false
  messageType: string
  messageTimestamp: number
  device: string
}

export default VideoMessageType
