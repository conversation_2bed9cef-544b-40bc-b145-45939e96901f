import { GenericError } from './BaseErrors/GenericError'
import { NotFoundError } from './BaseErrors/NotFoundError'
import { UnauthorizedError } from './BaseErrors/UnauthorizedError'

export class InstagramErrors {
  public response: any
  constructor(response: any) {
    this.response = response
  }

  sendMessage() {
    if (
      this.response.error &&
      this.response.data.message.error.code === 100 &&
      this.response.data.message.error.error_subcode === 2534014
    ) {
      throw new NotFoundError(
        'Usuário do Instagram não encontrado, cheque a lista de interações, e vincule novamente',
        this.response
      )
    } else if (
      this.response.error &&
      this.response.status == 400 &&
      this.response.data.message.error.code === 190
    ) {
      this.response.disconnect = true
      throw new GenericError(
        'Seu token do ShotX expirou, por favor conecte novamente',
        this.response
      )
    } else if (
      this.response.error &&
      this.response.status === 403 &&
      this.response.data.message.error.error_subcode === 2534022
    ) {
      throw new UnauthorizedError(
        'Mensagem enviada fora do período de resposta permitido pela Meta',
        this.response
      )
    } else if (
      this.response.error &&
      this.response.status === 403 &&
      this.response.data.message.error.code === 10
    ) {
      //NÃO DESCONECTAR INSTÂNCIA, POIS O WEBHOOK COM AS MENSAGENS AINDA CONTINUAM CHEGANDO, MESMO SEM A PERMISSÃO DE GERENCIAR MENSAGENS
      throw new UnauthorizedError(
        'Permissão de enviar mensagem removida do Instagram. Favor desconectar a instância e conectar novamente',
        this.response
      )
    } else if (this.response.error) {
      throw new GenericError(
        'Ocorreu um erro, tente novamente em alguns minutos, ou entre em contato com o suporte',
        this.response
      )
    }

    return this.response
  }
}
