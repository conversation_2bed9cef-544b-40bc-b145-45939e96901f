import ffmpegPath from '@ffmpeg-installer/ffmpeg';
import axios from 'axios';
import { isURL } from 'class-validator';
import ffmpeg from 'fluent-ffmpeg';
import { promises as fsPromises } from 'fs';
import os from 'os';
import path from 'path';
import { PassThrough } from 'stream';
import { MinioFile } from '../libs/types/minio/file';

// Diretório temporário para armazenar arquivos recebidos
const TEMP_DIR = path.join(os.tmpdir(), 'uploads');


const audioMimeTypes = new Map([
  ['audio/mpeg', 'mp3'],        // MP3
  ['audio/wav', 'wav'],         // WAV
  ['audio/ogg', 'ogg'],         // OGG
  ['audio/x-ms-wma', 'wma'],    // WMA
  ['audio/aac', 'aac'],         // AAC
  ['audio/flac', 'flac'],       // FLAC
  ['audio/x-wav', 'wav'],       // WAV (variante)
  ['audio/x-m4a', 'm4a'],       // M4A (variante)
  ['audio/mp4', 'mp4'],         // MP4 (para áudio)
  ['audio/webm', 'webm'],       // WEBM
  ['audio/x-aiff', 'aiff'],     // AIFF
  ['audio/x-caf', 'caf'],       // CAF
  ['audio/midi', 'mid'],        // MIDI
  ['audio/x-aac', 'aac'],       // AAC (variante)
  ['audio/vnd.wave', 'wav']     // WAV (alternativo)
]);

export const getExtensionFromMimeType = (mime: string) => audioMimeTypes.get(mime)

/**
 * Converte um Blob de OGG para um Blob de M4A
 * @param inputBlob - Buffer de entrada no formato OGG
 * @returns Uma Promise que resolve com o Buffer convertido para M4A
 */
export const convertOggToM4aFromBlob = async (file: MinioFile): Promise<MinioFile> => {
  return new Promise((resolve, reject) => {

    // Gerar um nome de arquivo temporário único
    const tempInputFile = `${file.originalname}`;
    const tempOutputFile = `${file.originalname}`.replace('.ogg', '.m4a');

    // Salvar o Blob em um arquivo temporário
    fsPromises.writeFile(tempInputFile, file.buffer)
      .then(() => {
        ffmpeg(tempInputFile)
          .output(tempOutputFile)
          .audioCodec('aac')
          .on('end', async () => {
            try {
              // Ler o arquivo convertido
              const outputBlob = await fsPromises.readFile(tempOutputFile);
              let outputFile = file
              outputFile.buffer = outputBlob
              outputFile.encoding = 'acc'
              outputFile.originalname = tempOutputFile
              outputFile.mimetype = 'audio/m4a'
              resolve(outputFile);
            } catch (err) {
              reject(err);
            } finally {
              // Limpar arquivos temporários
              fsPromises.unlink(tempInputFile).catch(console.error);
              fsPromises.unlink(tempOutputFile).catch(console.error);
            }
          })
          .on('error', (err: Error) => {
            reject(err);
            // Limpar arquivos temporários em caso de erro
            fsPromises.unlink(tempInputFile).catch(console.error);
            fsPromises.unlink(tempOutputFile).catch(console.error);
          })
          .run();
      })
      .catch(reject);
  });
};

export const processAudio = async (audio: string): Promise<Buffer> => {
  let inputAudioStream: PassThrough;

  if (isURL(audio)) {
    const timestamp = new Date().getTime();
    const url = `${audio}?timestamp=${timestamp}`;

    const config: any = {
      responseType: 'stream',
    };

    const response = await axios.get(url, config);
    inputAudioStream = response.data.pipe(new PassThrough());
  } else {
    const audioBuffer = Buffer.from(audio, 'base64');
    inputAudioStream = new PassThrough();
    inputAudioStream.end(audioBuffer);
  }

  return new Promise((resolve, reject) => {
    const outputAudioStream = new PassThrough();
    const chunks: Buffer[] = [];

    outputAudioStream.on('data', (chunk) => chunks.push(chunk));
    outputAudioStream.on('end', () => {
      const outputBuffer = Buffer.concat(chunks);
      resolve(outputBuffer);
    });

    outputAudioStream.on('error', (error) => {
      console.log('error', error);
      reject(error);
    });

    ffmpeg.setFfmpegPath(ffmpegPath.path);

    ffmpeg(inputAudioStream)
      .outputFormat('ogg')
      .noVideo()
      .audioCodec('libopus')
      .addOutputOptions('-avoid_negative_ts make_zero')
      .audioChannels(1)
      .pipe(outputAudioStream, { end: true })
      .on('error', function (error: any) {
        console.log('error', error);
        reject(error);
      });
  });
}

export const processAudioMp4 = async (audio: string) => {
  let inputAudioStream: PassThrough;

  if (isURL(audio)) {
    const timestamp = new Date().getTime();
    const url = `${audio}?timestamp=${timestamp}`;

    const config: any = {
      responseType: 'stream',
    };

    const response = await axios.get(url, config);
    inputAudioStream = response.data.pipe(new PassThrough());
  } else {
    const audioBuffer = Buffer.from(audio, 'base64');
    inputAudioStream = new PassThrough();
    inputAudioStream.end(audioBuffer);
  }

  return new Promise((resolve, reject) => {
    const outputAudioStream = new PassThrough();
    const chunks: Buffer[] = [];

    outputAudioStream.on('data', (chunk) => chunks.push(chunk));
    outputAudioStream.on('end', () => {
      const outputBuffer = Buffer.concat(chunks);
      resolve(outputBuffer);
    });

    outputAudioStream.on('error', (error) => {
      console.log('error', error);
      reject(error);
    });

    console.log('ffmpegPath', ffmpegPath)
    ffmpeg.setFfmpegPath(ffmpegPath.path);

    ffmpeg(inputAudioStream)
      .outputFormat('mp4')
      .noVideo()
      .audioCodec('aac')
      .audioBitrate('128k')
      .audioFrequency(44100)
      .addOutputOptions('-f ipod')
      .pipe(outputAudioStream, { end: true })
      .on('error', function (error: any) {
        console.log('error', error);
        reject(error);
      });
  });
}

export const processAudioBufferToMp4 = async (audio: Buffer) => {
  console.log('Buffer :', audio);

  let inputAudioStream = new PassThrough();
  inputAudioStream.end(audio);

  return new Promise((resolve, reject) => {
    const outputAudioStream = new PassThrough();
    const chunks: Buffer[] = [];

    outputAudioStream.on('data', (chunk) => chunks.push(chunk));
    outputAudioStream.on('end', () => {
      const outputBuffer = Buffer.concat(chunks);
      resolve(outputBuffer);
    });

    outputAudioStream.on('error', (error) => {
      console.log('error', error);
      reject(error);
    });

    console.log('ffmpegPath', ffmpegPath)
    ffmpeg.setFfmpegPath(ffmpegPath.path);

    ffmpeg(inputAudioStream)
      .outputFormat('mp4')
      .noVideo()
      .audioCodec('aac')
      .audioBitrate('128k')
      .audioFrequency(44100)
      // .addOutputOptions('-f ipod')
      .pipe(outputAudioStream, { end: true })
      .on('error', function (error: any) {
        console.log('error', error);
        reject(error);
      });
  });
}

// /**
//  * Baixa um arquivo de áudio de uma URL e converte para base64.
//  * @param {string} url - URL do arquivo de áudio.
//  * @returns {Promise<string>} - String base64 do arquivo de áudio.
//  */
// export async function downloadAudioAsBase64(url: string) {
//   try {
//     // Faz o download do arquivo de áudio usando o axios
//     const response = await axios({
//       method: 'get',
//       url: url,
//       responseType: 'arraybuffer' // Recebe a resposta como arraybuffer (binário)
//     });

//     // Converte o ArrayBuffer para base64
//     const base64Audio = Buffer.from(response.data, 'binary').toString('base64');

//     // Retorna a string base64 com o prefixo de tipo de áudio
//     return `data:audio/mp3;base64,${base64Audio}`;
//   } catch (error: any) {
//     console.error('Erro ao baixar o arquivo de áudio:', error.message);
//     throw error;
//   }
// }

// /**
//  * Baixa um arquivo de áudio de uma URL e retorna no formato MinioFile.
//  * @param {string} url - URL do arquivo de áudio.
//  * @param {string} fieldname - Nome do campo (usado na estrutura MinioFile).
//  * @param {string} originalname - Nome original do arquivo.
//  * @returns {Promise<MinioFile>} - Objeto do tipo MinioFile com os dados do áudio.
//  */
// export async function downloadAudioAsMinioFile(url: string, fieldname: string, originalname: string) {
//   try {
//     // Faz o download do arquivo de áudio usando o axios
//     const response = await axios({
//       method: 'get',
//       url: url,
//       responseType: 'arraybuffer' // Recebe a resposta como arraybuffer (binário)
//     });

//     // Converte o ArrayBuffer para um Buffer do Node.js
//     const buffer = Buffer.from(response.data, 'binary');

//     // Extrai o mimetype e encoding do cabeçalho da resposta
//     const mimetype = response.headers['content-type'] || 'application/octet-stream';
//     const encoding = response.headers['content-encoding'] || '7bit';  // Default to '7bit' if not provided

//     // Cria o objeto MinioFile
//     const minioFile = {
//       fieldname: fieldname,             // Nome do campo (pode ser customizado)
//       originalname: originalname,       // Nome original do arquivo
//       encoding: mimetype,                 // Encoding (padrão)
//       mimetype: encoding,            // Tipo MIME (ajuste conforme o tipo de áudio)
//       buffer: buffer,                   // O buffer do arquivo de áudio
//       size: buffer.length               // Tamanho do arquivo em bytes
//     };

//     return minioFile;
//   } catch (error: any) {
//     console.error('Erro ao baixar o arquivo de áudio:', error.message);
//     throw error;
//   }
// }