import { firestore } from '../../../config'
import { FIRESTORE_COLLECTIONS } from '../../../constants/collections'

export const qiUser = async (userId: string) => {
  const userDoc = await firestore
    .collection(FIRESTORE_COLLECTIONS.QIUSERS)
    .doc(userId)
    .get()

  return userDoc
}

export const accountIdByUserId = async (userId: string) => {
  try {
    const userDoc: any = await firestore
      .collection(FIRESTORE_COLLECTIONS.QIUSERS)
      .doc(userId)
      .get()

    const user = userDoc.data()

    return user?.accountId
  } catch (error) {
    return null
  }
}

export const userById = async (id: string) => {
  try {
    const userRef = await firestore
      .collection(FIRESTORE_COLLECTIONS.QIUSERS)
      .where('id', '==', id)
      .get()

    const user = userRef.docs.length ? userRef.docs[0].data() : null

    return user
  } catch (error) {
    console.log('Failed to get user by uid', error)
    return null
  }
}

export const QiUserFirestoreService = {
  get: qiUser,
  accountIdByUserId,
}
