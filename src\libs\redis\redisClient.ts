import Redis from 'ioredis'

export const redisClient = new Redis({
  host: '**************',
  port: 6379,
  password: 'senh<PERSON><PERSON>uitoBo@0192',
  db: 0, // número do banco (0 é o padrão)
})

export async function testarConexao() {
  try {
    const resposta = await redisClient.ping()
    console.log('Conectado ao Redis:', resposta) // deve imprimir "PONG"
  } catch (erro) {
    console.error('Erro ao conectar ao Redis:', erro)
  }
}

export async function updateKeyInRedis(
  key: string,
  infoKey: any,
  infoValue: any
) {
  const initialInfo = await redisClient.get(key)
  console.log('INSIDE UPDATE KEY', initialInfo)
  if (initialInfo) {
    console.log('INSIDE UPDATE KEY IF')
    const data = JSON.parse(initialInfo)

    data[infoKey] = infoValue

    console.log('DATA INSIDE UPDATE KEY IN REDIS', data)
    console.log('DAT<PERSON> INSIDE UPDATE KEY IN REDIS ATTEMPS', data.attempts)
    await redisClient.set(key, JSON.stringify(data))
    return
  }
  console.log('INSIDE UPDATE KEY WiTHOUT IF')

  return null
}

export async function deleteKey(key: string) {
  try {
    const resultado = await redisClient.del(key)
    if (resultado === 1) {
      console.log(`Chave "${key}" removida com sucesso!`)
    } else {
      console.log(`Chave "${key}" não encontrada.`)
    }
  } catch (erro) {
    console.error('Erro ao remover a chave:', erro)
  }
}
