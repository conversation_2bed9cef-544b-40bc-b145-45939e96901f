// export const getFieldValue = (
//   field: any,
//   data: any,
//   validate: any,
//   fetchedData: any,
//   arrayIndex: any
// ) => {
//   const {
//     key,
//     name,
//     default_value,
//     type,
//     calc,
//     fetch_value,
//     fetch_default,
//     min,
//     max,
//   } = field

//   if (calc) {
//     return calcFieldValue(field, data, validate, fetchedData, arrayIndex)
//   } else if (fetch_value || fetch_default) {
//     return getFetchedValue(field, data, validate, fetchedData, arrayIndex)
//   }

//   // const keysArray = (key || '').split(':')

//   const evalData = data || {}

//   // keysArray.forEach((a, c) => {
//   //   const k = a === '#' ? arrayIndex : a

//   //   if (k !== undefined && isLoopable(evalData) && k in evalData) {
//   //     evalData = evalData[k]
//   //   }
//   // })

//   let val =
//     isLoopableObject(evalData) && name in evalData
//       ? evalData[name]
//       : 'default_value' in field
//         ? default_value
//         : null

//   if (validate) {
//     switch (type) {
//       case 'percent':
//       case 'number':
//         val = typeof min === 'number' ? Math.max(min, val) : val
//         val = typeof max === 'number' ? Math.min(max, val) : val

//         if (type === 'percent') {
//           val = Math.min(100, Math.max(0, val))
//         }

//         break

//       default:
//         break
//     }
//   }

//   return val
// }

// export const calcFieldValue = (
//   field: any,
//   data: any,
//   validate: any,
//   fetchedData: any,
//   arrayIndex: any
// ) => {
//   const { name, default_value, return_type, type, calc, min, max } = field

//   const returnType = return_type || 'float'

//   let total = 0

//   if (Array.isArray(calc)) {
//     // console.groupCollapsed(`calcFieldValue > ${name}`);

//     const calcFields = calc.filter((f) =>
//       filterFields(f, calc, data, fetchedData, arrayIndex)
//     )

//     const values = calcFields.map((c) => {
//       const { name, operator, sub_field, multiplier } = c

//       const fieldValue = getFieldValue(
//         c,
//         data,
//         validate,
//         fetchedData,
//         arrayIndex
//       )
//       const subCalc = c.calc

//       let val

//       if (Array.isArray(fieldValue)) {
//         let subtotal = 0
//         const items = fieldValue

//         items.forEach((item, i) => {
//           if (sub_field && isLoopableObject(item) && sub_field in item) {
//             const multiplierVal = multiplier
//               ? getFieldValue(
//                 { key: `${name}:#`, name: multiplier },
//                 data,
//                 validate,
//                 fetchedData,
//                 i
//               )
//               : 1
//             let subval = getFieldValue(
//               { key: `${name}:#`, name: sub_field },
//               data,
//               validate,
//               fetchedData,
//               i
//             )

//             subval = sanitizeVal(subval, returnType, 0)
//             if (multiplierVal) {
//               subval = calculate('*', subval, multiplierVal)
//             }
//             subtotal = calculate(operator, subval, subtotal)
//           }
//         })

//         val = subtotal
//       } else {
//         if (isLoopableObject(fieldValue) && fieldValue[name]) {
//           const subval = sanitizeVal(fieldValue[name], returnType, 0)
//           val = calculate(operator, subval, 0)
//         } else {
//           val = sanitizeVal(fieldValue, returnType, 0)
//         }
//       }

//       total = calculate(operator, val, total)

//       return val
//     })
//   }

//   return !isNaN(total) ? total : 'default_value' in field ? default_value : 0
// }

// export const filterFields = (
//   field: any,
//   fields: any,
//   data: any,
//   fetchedData: any,
//   arrayIndex: any
// ) => {
//   const { conditional_logic } = field

//   if (Array.isArray(conditional_logic) && conditional_logic.length) {
//     // console.groupCollapsed(`filterFields > ${field.label}`);
//     // console.log(field.label);

//     const match = conditional_logic.find((or) => {
//       return (
//         or.length ===
//         or.filter((and: any) => {
//           let compare = and.value
//           const operator = and.operator

//           if (typeof compare === 'string' && compare.split(':').length > 1) {
//             const keyArr = compare.split(':')
//             const targetIndicator = keyArr.splice(0, 1)[0]
//             const targetName = keyArr.pop()
//             const targetKey = keyArr.join(':')
//             const targetCollection = and.collection
//             const targetID = and.ID
//             const isSelf = targetIndicator === 'self'
//             const targetData = isSelf
//               ? data
//               : fetchedData.find(
//                 (p: any) =>
//                   p.collection === targetCollection && p.ID === targetID
//               )

//             compare = getFieldValue(
//               { name: targetName, key: targetKey },
//               targetData,
//               false,
//               fetchedData,
//               null
//             )

//             // console.log('targetName',targetName);
//             // console.log('targetKey',targetKey);
//             // console.log('compare',compare);
//             // console.log('targetData',targetData);
//           }

//           const targetField = fields.find(
//             (f: any) => f.field === and.field
//           ) || {
//             ...and,
//             key: and.key,
//             name: and.field,
//           }

//           const val = getFieldValue(
//             targetField,
//             data,
//             false,
//             fetchedData,
//             arrayIndex
//           )

//           let valMatch = false

//           switch (operator) {
//             case '==':
//               valMatch = val === compare
//               break
//             case '!=':
//               valMatch = val !== compare
//               break
//             case '==contains':
//               valMatch = val && val.indexOf && val.indexOf(compare) >= 0
//               break
//             case '!=contains':
//               valMatch = !val || (val.indexOf && val.indexOf(compare) === -1)
//               break
//             case '!=empty':
//               valMatch =
//                 val !== 0 &&
//                 val !== '' &&
//                 val !== null &&
//                 val !== undefined &&
//                 val !== 'undefined'
//               break
//             case '==empty':
//               valMatch = val === '' || val === null
//               break
//             default:
//               break
//           }
//           return valMatch
//         }).length
//       )
//     })
//     return match
//   }

//   return true
// }

// export function isLoopableObject(obj: any) {
//   return (
//     Boolean(obj) &&
//     !Array.isArray(obj) &&
//     typeof obj === 'object' &&
//     Object.keys(obj) &&
//     Object.keys(obj).length
//   )
// }

// export const sanitizeVal = (val: any, returnType: any, defaultVal: any) => {
//   if (val) {
//     switch (returnType) {
//       case 'float':
//         val = parseFloat(val)
//         break
//       case 'int':
//       case 'integer':
//         val = parseInt(val, 10)
//         break
//       default:
//         break
//     }
//   }
//   return !isNaN(val) ? val : defaultVal || 0
// }

// export const calculate = (operator: any, v: any, t: any) => {
//   if (!isNaN(Number(t)) && !isNaN(Number(v))) {
//     t = Number(t)
//     v = Number(v)
//     switch (operator) {
//       case '+':
//         t += v
//         break
//       case '-':
//         t -= v
//         break
//       case '*':
//         t = t * v
//         break
//       case '/':
//         t = t / (v || 1)
//         break
//       case '%':
//         t = t % v
//         break
//       case '+%':
//         t = t + t * (v / 100)
//         break
//       case '-%':
//         t = t - t * (v / 100)
//         break
//       default:
//         break
//     }
//   }
//   return t
// }

// export const getFetchedValue = (
//   field: any,
//   data: any,
//   validate: any,
//   fetchedData: any,
//   arrayIndex: any
// ): any => {
//   const { fetch_value, fetch_default, default_value, fetch } = field

//   let val

//   if (
//     (fetch_value || fetch_default) &&
//     fetchedData &&
//     isLoopableObject(fetch)
//   ) {
//     const { collection, value } = fetch

//     const items = Array.isArray(fetchedData)
//       ? fetchedData
//       : isLoopableObject(fetchedData) && Array.isArray(fetchedData[collection])
//         ? fetchedData[collection]
//         : []

//     const itemId = getFieldValue(fetch, data, false, null, arrayIndex)

//     if (itemId) {
//       const fetchedItem = items.find((item) => item.ID === itemId)

//       if (fetchedItem) {
//         const keysArray = (value || '').split(':')
//         const name = keysArray.length === 1 ? keysArray[0] : keysArray.pop()
//         const pseudo = { key: keysArray.join(':'), name }

//         val = getFieldValue(
//           pseudo,
//           fetchedItem,
//           validate,
//           fetchedData,
//           arrayIndex
//         )
//       }
//     }
//   }
//   return val !== undefined ? val : 'default_value' in field ? default_value : ''
// }
