import { Request, Response, Router } from 'express'
import { <PERSON>niper<PERSON><PERSON>roller } from '../controllers/sniper.controller'
import { InstanceMiddleware } from '../middlewares/instance.middleware'
import { SniperInitMiddleware } from '../middlewares/sniper.init.middleware'

export const router = Router()

const RESOURCE = '/sniper'

router.put(
  `${RESOURCE}/change_session/:instanceId`,
  async (req: Request, res: Response) => {
    const { instanceId } = req.params
    const { accountId, action, jwt, contactId, platform } = req.body
    console.log('AZAP', instanceId, req.body)
    if (!instanceId || !accountId || !action || !contactId || !platform) {
      res.status(400).send({
        error: true,
        data: 'instanceId, accountId, contactId, platform and action are required',
      })
      return
    }

    const controller = new SniperController()
    const response = await controller.setSessionStatus(
      instanceId,
      accountId,
      contactId,
      action
    )

    res.send(response)
  }
)

router.post(
  `${RESOURCE}/all/`,
  InstanceMiddleware,
  async (req: Request, res: Response) => {
    const { instance } = req.body

    const { accountId } = instance

    const controller = new SniperController()
    const sessions = await controller.getSnipers(accountId)

    res.send({
      error: false,
      data: sessions,
    })
  }
)

router.post(
  `${RESOURCE}/getCurrentSession/:contactId`,
  InstanceMiddleware,
  async (req: Request, res: Response) => {
    const { contactId } = req.params
    const { instance } = req.body

    const { id, accountId } = instance

    const controller = new SniperController()
    const session = await controller.getSession(id, accountId, contactId)

    res.send({
      error: false,
      data: session,
    })
  }
)
router.post(
  `${RESOURCE}/getSessions/:contactId`,
  InstanceMiddleware,
  async (req: Request, res: Response) => {
    const { contactId } = req.params
    const { instance } = req.body

    const { id, accountId } = instance

    const controller = new SniperController()
    const sessions = await controller.getSessionsOpened(id, accountId, contactId)

    res.send({
      error: false,
      data: sessions,
    })
  }
)

router.post(
  `${RESOURCE}/sendToSniper/:contactId`,
  SniperInitMiddleware,
  async (req: Request, res: Response) => {
    const { contactId } = req.params
    const { instance, sniper, contactRemoteId, contactName } = req.body

    const controller = new SniperController()
    const result = await controller.initSniperManually(
      instance,
      sniper,
      contactId, // Whatsapp numero || Instagram ig_sid
      contactRemoteId, // Whatsapp numero || Instagram username
      contactName // Whatsapp pushname || Instagram name
    )

    res.send({
      error: result.error,
      data: {
        message: result.message,
      },
    })
  }
)

router.post(
  `${RESOURCE}/getSniperAccountData`,
  async (req: Request, res: Response) => {
    const controller = new SniperController()
    const { accountId } = req.body
    if (!accountId) {
      return res.status(400).send({
        error: true,
        data: 'accountId required',
      })
    }

    const snipers = await controller.getSniperAccountData(accountId)

    res.send({
      error: false,
      data: snipers,
    })
  }
)

router.post(
  `${RESOURCE}/broadcast`,
  // SniperInitMiddleware,
  async (req: Request, res: Response) => {
    // const { instance, sniper, contactRemoteId, contactName } = req.body
    // RabbitMQIntercept<any>({
    //   queueName: `${RESOURCE}/broadcast`,
    //   data: req.body,
    //   onQueue(sended) {
    //     res.send('ok')
    //   },
    //   async onReceive(data) {
    const { shotxCrons } = req.body // => [{}]
    const controller = new SniperController()

    controller.sendBroadcast(shotxCrons)
    res.send('ok')
    //   },
    // })
  }
)

router.post(`${RESOURCE}/getSniper`, async (req: Request, res: Response) => {
  const controller = new SniperController()
  const { privateSniperId, apiKey } = req.body

  if (!privateSniperId || !apiKey) {
    res.send({
      error: true,
      data: 'privateSniperId e apiKey precisam ser informados',
    })
    return
  }

  const snipers = await controller.getSniper(privateSniperId, apiKey)

  res.send({
    error: false,
    data: snipers,
  })
})

export { router as sniperRouter }
