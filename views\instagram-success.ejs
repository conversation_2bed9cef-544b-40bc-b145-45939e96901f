<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <link rel="stylesheet" type="text/css" href="inddex.css" media="screen" />
  <title>QIPLUS | INSTAGRAM LOGIN</title>
</head>

<body>
  <div class="login-container">
    <div class="login-animation">
      <span class="checkmark"></span>
    </div>
    <p class="welcome-message">Login finalizado. Obrigado por usar QIPlus!</p>
    <p class="finish-message">Por favor, pode fechar esta janela.</p>
  </div>
</body>
<style>
  /* Container e elementos principais */
  .login-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 100vh;
    background-color: #f09433;
    /* Cores estilo Instagram */
    background: linear-gradient(45deg,
        #bc1888,
        #cc2366,
        #e6683c,
        #f09433,
        #dc2743);
    font-family: "Arial", sans-serif;
  }

  .login-animation {
    position: relative;
    width: 50px;
    height: 50px;
    margin-bottom: 20px;
    animation: pop-in 0.5s ease-out forwards;
  }

  /* Animação do checkmark */
  .checkmark {
    position: absolute;
    width: 25px;
    height: 50px;
    border-radius: 5px;
    border: solid 5px white;
    border-width: 0 5px 5px 0;
    transform: rotate(45deg);
    opacity: 0;
    animation: checkmark 0.5s ease-out 0.5s forwards;
  }

  /* Animação de aparecer do checkmark */
  @keyframes checkmark {
    from {
      transform: rotate(0deg);
      opacity: 0;
    }

    to {
      transform: rotate(45deg);
      opacity: 1;
    }
  }

  /* Animação do container principal */
  @keyframes pop-in {
    0% {
      transform: scale(0.5);
      opacity: 0;
    }

    100% {
      transform: scale(1);
      opacity: 1;
    }
  }

  /* Mensagem de boas-vindas */
  .welcome-message {
    color: white;
    font-size: 18px;
    text-align: center;
    opacity: 0;
    animation: fade-in 1s ease-out 1s forwards;
  }

  .finish-message {
    color: white;
    font-size: 14px;
    text-align: center;
    opacity: 0;
    animation: fade-in 1s ease-out 1s forwards;
  }

  /* Animação de fade-in para a mensagem */
  @keyframes fade-in {
    from {
      opacity: 0;
    }

    to {
      opacity: 1;
    }
  }
</style>

</html>