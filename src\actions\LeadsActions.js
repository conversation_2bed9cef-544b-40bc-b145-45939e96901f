/**
 * Leads Actions
 */
import { FirestoreRef, createLead } from 'FirebaseRef'

// lang strings
import { langMessages } from '../lang'

import { NotificationManager } from 'react-notifications'

import {
  FIRESTORE_ERROR,
  GET_LEADS_KEYWORDS,
  GET_POSTS,
  GET_POSTS_FAILURE,
  GET_POSTS_SUCCESS,
  LEAD_MODAL_CLOSE,
  LEAD_MODAL_OPEN,
  SAVE_NEW_POST,
  SAVE_NEW_POST_FAILURE,
  SAVE_NEW_POST_SUCCESS,
  SAVE_POST,
  SAVE_POST_FAILURE,
  SAVE_POST_SUCCESS,
} from './types'

import { isLoopableObject, localJSON, toSearchKeyword } from '../helpers/helpers'

import { DEFAULT_LOCALE, LEADS_COLLECTION_NAME, MOMENT_ISO } from 'Constants'
import { MANAGER_LEVEL, SELLER_LEVEL, SELLER_ROLE } from 'Constants/UsersRoles'
import moment from 'moment'

import LeadModel from 'Routes/leads/model'

const collectionName = LEADS_COLLECTION_NAME

/**
 * Redux Action Get Leads
 */

export const getLeads = (accountId, where, params) => (dispatch, getState) => {
  const {
    authReducer: { account },
  } = getState()
  accountId = accountId || account.ID

  dispatch({ type: GET_POSTS, collection: collectionName })

  return new Promise((resolve, reject) => {
    const queries = Array.isArray(where) ? where : []
    const qObj = isLoopableObject(where) ? where : params || {}
    const user = localJSON.get('user', false)

    let QueryRef = FirestoreRef.collection(collectionName)
    if (accountId !== 1) {
      if (user.level === SELLER_LEVEL) {
        QueryRef = QueryRef.where(SELLER_ROLE, '==', `${user.ID}`)
      } else {
        QueryRef = QueryRef.where('accountId', '==', `${accountId}`)
      }
    }

    Array.isArray(queries) && queries.forEach(queryArgs => (QueryRef = QueryRef.where(queryArgs[0], queryArgs[1], queryArgs[2])))

    if (qObj.searchTerm) {
      const keyword = toSearchKeyword(qObj.searchTerm)
      QueryRef = QueryRef.where('keywords', 'array-contains', `${keyword}`)
      if (!queries.length) {
        const keywords = [keyword]
        dispatch({ type: GET_LEADS_KEYWORDS, keywords })
      }
    } else if (Array.isArray(qObj.keywords)) {
      const keywords = qObj.keywords.map(w => toSearchKeyword(w))
      QueryRef = QueryRef.where('keywords', 'array-contains-any', keywords)

      if (!queries.length) {
        dispatch({ type: GET_LEADS_KEYWORDS, keywords })
      }
    } else {
      if (qObj.orderBy) QueryRef = QueryRef.orderBy(qObj.orderBy)
    }

    if (qObj.limit) QueryRef = QueryRef.limit(qObj.limit)
    if (qObj.count) QueryRef = QueryRef.count()

    QueryRef.get()
      .then(snapshot => {
        const leads = []
        snapshot.forEach(doc => {
          leads.push(doc.data())
        })
        dispatch({
          type: GET_POSTS_SUCCESS,
          collection: collectionName,
          payload: leads,
        })
        return resolve(leads)
      })
      .catch(function (error) {
        console.error(error)
        dispatch({ type: GET_POSTS_FAILURE, collection: collectionName })
        dispatch({ type: FIRESTORE_ERROR, error })
        return reject(error)
      })
  })
}

/**
 * Redux Action listen Lead
 */
export const saveNewLead = data => (dispatch, getState) => {
  dispatch({ type: SAVE_NEW_POST, collection: collectionName })
  return new Promise((resolve, reject) => {
    const { authReducer } = getState()
    const newLead = sanitizeLead(data, 'add', authReducer)

    if (!newLead.accountId) {
      return reject({ message: langMessages['errors.genericErrorMsg'] })
    }

    createLead(newLead)
      .then(response => {
        // console.log('createLead > response',response);
        let result = response.data
        if (result.error) {
          let errorMsg = langMessages['errors.genericErrorMsg']
          switch (result.error) {
            case 'MODULE_NOT_AVAILABLE':
              errorMsg = langMessages['errors.MODULE_NOT_AVAILABLE']
              break
            case 'MODULE_LIMIT_REACHED':
              errorMsg = langMessages['placeholders.MODULE_LIMIT_REACHED'].replace(
                '[%s]',
                langMessages[`modules.${collectionName}`] || collectionName
              )
              break
            default:
              break
          }
          return reject({ message: errorMsg, error: result.error })
        }

        const payload = result
        dispatch({
          type: SAVE_NEW_POST_SUCCESS,
          collection: collectionName,
          payload,
        })
        return resolve(payload)
      })
      .catch(function (error) {
        console.error(error)
        dispatch({ type: FIRESTORE_ERROR, error })
        return reject({ message: langMessages['errors.dbErrorMsg'], error })
      })
  }).catch(function (error) {
    console.error(error)
    if (error.message) {
      NotificationManager.error(error.message)
    }
    dispatch({
      type: SAVE_NEW_POST_FAILURE,
      collection: collectionName,
      error,
      message: error.message || langMessages['errors.genericErrorMsg'],
    })
  })
}

export const saveLead = data => (dispatch, getState) => {
  const { ID } = data
  dispatch({ type: SAVE_POST, collection: collectionName })
  return new Promise((resolve, reject) => {
    if (!ID) {
      dispatch({ type: SAVE_POST_FAILURE, collection: collectionName })
      return reject('SAVE_POST_FAILURE: No ID provided')
    }

    const { authReducer } = getState()
    const lead = sanitizeLead(data, 'update', authReducer)
    // console.log('saveLead > lead',lead);

    FirestoreRef.collection(collectionName)
      .doc(ID)
      .update(lead)
      .then(() => {
        const payload = lead
        dispatch({
          type: SAVE_POST_SUCCESS,
          collection: collectionName,
          payload,
        })
        return resolve(payload)
      })
      .catch(function (error) {
        dispatch({ type: SAVE_POST_FAILURE, collection: collectionName })
        dispatch({ type: FIRESTORE_ERROR, error })
        return reject()
      })
  })
}

export const openLeadModal = id => ({
  type: LEAD_MODAL_OPEN,
  payload: id,
})

export const closeLeadModal = () => ({
  type: LEAD_MODAL_CLOSE,
})

export const getSaveComments = (contentComments, owner) => {
  let comments = [
    {
      content: contentComments,
      context: {
        collection: 'leads',
        operator_id: owner,
      },
      date: moment().format(MOMENT_ISO),
      operator_id: owner,
    },
  ]

  return comments
}

export const sanitizeLead = (data, prepare, authReducer, insertedBy = null) => {
  const { user, ownerId, account } = authReducer

  // owner
  const accountId = data.accountId || account.ID || user.accountId
  // end owner

  const seller = data.seller || (user.level === SELLER_LEVEL && user.ID) || ''
  const manager = data.manager || user.manager || (user.level === MANAGER_LEVEL && user.ID) || ''

  let dbData = {
    ...LeadModel,
    ...window.jsonClone(data),
    collection: collectionName,
    locale: data.locale || DEFAULT_LOCALE,
    seller,
    manager,
    owner: ownerId,
    accountId,
  }

  switch (prepare) {
    case 'add':
      dbData = {
        ...dbData,
        date: moment().format(MOMENT_ISO),
        modified: moment().format(MOMENT_ISO),
        author: user.ID,
        logs: {
          ...(data.logs || {}),
          added: {
            user: user.ID,
            operator_id: user.ID,
            date: moment().format(MOMENT_ISO),
            inserted: insertedBy,
          },
        },
      }

      break

    case 'update':
    default:
      dbData = {
        ...dbData,
        modified: moment().format(MOMENT_ISO),
        logs: {
          ...(data.logs || {}),
          updated: {
            user: user.ID,
            operator_id: user.ID,
            date: moment().format(MOMENT_ISO),
          },
        },
      }

      break
  }

  return dbData
}

export const getLeadsByShotxPlatform = (instance, accountId, where = {}) => {
  const c = []
  const f = []
  if (instance.platform.toLowerCase() === 'instagram') {
    const collection = `shotx/${accountId}/instances/${instance.id}/contacts`
    return FirestoreRef.collection(collection).get().then(snapshot => {
      snapshot.docs.map(doc => {
        const data = doc.data()
        if (data.lead_id) {
          f.push(data.lead_id)
        }
      });
      if (f.length > 0) {
        console.log('f', f);
        return FirestoreRef.collection(collectionName)
          .where('accountId', '==', accountId)
          .where('status', '!=', 'trash')
          .where('ID', 'in', f).get().then(snapshot => {
            snapshot.docs.map(doc => {
              const data = doc.data()
              c.push(data)
            });
            console.log('c', c);
            return c
          })
      }
    })
  } else {
    return FirestoreRef.collection(collectionName)
      .where('accountId', '==', accountId)
      .where('status', '!=', 'trash').get().then(snapshot => {
        snapshot.docs.map(doc => {
          const data = doc.data()
          if (data.mobileCC !== "" && data.mobile !== "") {
            c.push(data)
          }
        });
        return c
      })
  }

}
