import {
  <PERSON><PERSON>,
  <PERSON>,
  <PERSON><PERSON>,
  Card,
  CardContent,
  Chip,
  Divider,
  FormControl,
  InputLabel,
  LinearProgress,
  MenuItem,
  Paper,
  Select,
  Step,
  StepContent,
  Step<PERSON><PERSON>l,
  Stepper,
  TextField,
  Typography
} from '@material-ui/core'
import RctCollapsibleCard from 'Components/RctCollapsibleCard/RctCollapsibleCard'
import SimpleDualListBox from 'Components/UIComponents/duallistbox/SimpleDualListBox'
import { LEADS_COLLECTION_NAME } from 'Constants/AppCollections'
import { useSnackbar } from 'Contexts/snackbar'
import { FirestoreRef } from 'FirebaseRef'
import { langMessages } from 'Lang/index'
import React, { useCallback, useEffect, useState } from 'react'
import { Badge } from 'reactstrap'
import GroupStats from './components/GroupStats'

// Constantes
const MAX_LEADS_PER_GROUP = 500
const STEPS = [
  langMessages['whatsapp.groups.step.selectInstance'],
  langMessages['whatsapp.groups.step.selectLeads'],
  langMessages['whatsapp.groups.step.configureGroups'],
  langMessages['whatsapp.groups.step.createGroups']
]

const WhatsAppGroupManager = () => {
  const { account } = useSelector(state => state.authReducer)
  const { showSnackbar } = useSnackbar()

  // Estados principais
  const [activeStep, setActiveStep] = useState(0)
  const [instances, setInstances] = useState([])
  const [selectedInstance, setSelectedInstance] = useState('')
  const [allLeads, setAllLeads] = useState([])
  const [selectedLeads, setSelectedLeads] = useState([])
  const [loadingLeads, setLoadingLeads] = useState(false)
  const [loadingInstances, setLoadingInstances] = useState(false)
  const [groupPrefix, setGroupPrefix] = useState('')
  const [isCreatingGroups, setIsCreatingGroups] = useState(false)
  const [creationProgress, setCreationProgress] = useState(0)
  const [createdGroups, setCreatedGroups] = useState([])
  const [errors, setErrors] = useState([])

  // Carregar instâncias WhatsApp
  const loadInstances = useCallback(async () => {
    if (!account?.id) return

    setLoadingInstances(true)
    try {
      const query = FirestoreRef.collection('shotx')
        .where('accountId', '==', account.id)
        .where('platform', '==', 'Whatsapp')

      const snapshot = await query.get()
      const instancesData = snapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      }))

      setInstances(instancesData)
    } catch (error) {
      console.error('Erro ao carregar instâncias:', error)
      showSnackbar(langMessages['whatsapp.groups.error.loadInstances'], 'error')
    } finally {
      setLoadingInstances(false)
    }
  }, [account?.id, showSnackbar])

  // Carregar leads com nome e telefone
  const loadLeads = useCallback(async () => {
    if (!account?.id) return

    setLoadingLeads(true)
    try {
      let query = FirestoreRef.collection(LEADS_COLLECTION_NAME)
        .where('accountId', '==', account.id)
        .orderBy('firstName', 'asc')
        .limit(5000) // Limite conforme requisito

      const snapshot = await query.get()
      const leadsData = snapshot.docs
        .map(doc => {
          const data = doc.data()
          return {
            id: data.ID || doc.id,
            ...data
          }
        })
        .filter(lead => {
          // Filtrar leads com nome e telefone (conforme requisito)
          const hasName = (lead.firstName && lead.firstName.trim()) ||
            (lead.lastName && lead.lastName.trim()) ||
            (lead.displayName && lead.displayName.trim())
          const hasPhone = (lead.mobile && lead.mobile.trim()) ||
            (lead.phone && lead.phone.trim())
          return hasName && hasPhone
        })
        .map(lead => ({
          id: lead.id.toString(),
          title: lead.displayName || `${lead.firstName || ''} ${lead.lastName || ''}`.trim() || 'Sem nome',
          subtitle: `${lead.mobileCC || ''}${lead.mobile || lead.phone || ''}`,
          firstName: lead.firstName,
          lastName: lead.lastName,
          mobile: lead.mobile,
          mobileCC: lead.mobileCC,
          phone: lead.phone,
          phoneCC: lead.phoneCC
        }))

      setAllLeads(leadsData)
    } catch (error) {
      console.error('Erro ao carregar leads:', error)
      showSnackbar(langMessages['whatsapp.groups.error.loadLeads'], 'error')
    } finally {
      setLoadingLeads(false)
    }
  }, [account?.id, showSnackbar])

  // Efeitos
  useEffect(() => {
    loadInstances()
    loadLeads()
  }, [loadInstances, loadLeads])

  // Funções de navegação
  const handleNext = () => {
    setActiveStep(prevStep => prevStep + 1)
  }

  const handleBack = () => {
    setActiveStep(prevStep => prevStep - 1)
  }

  const handleReset = () => {
    setActiveStep(0)
    setSelectedInstance('')
    setSelectedLeads([])
    setGroupPrefix('')
    setCreatedGroups([])
    setErrors([])
    setCreationProgress(0)
  }

  // Calcular número de grupos necessários
  const calculateGroupsNeeded = () => {
    return Math.ceil(selectedLeads.length / MAX_LEADS_PER_GROUP)
  }

  // Dividir leads em grupos
  const divideLeadsIntoGroups = () => {
    const groups = []
    for (let i = 0; i < selectedLeads.length; i += MAX_LEADS_PER_GROUP) {
      groups.push(selectedLeads.slice(i, i + MAX_LEADS_PER_GROUP))
    }
    return groups
  }

  // Função para criar grupos (simulação - será implementada com API real)
  const createGroups = async () => {
    setIsCreatingGroups(true)
    setCreationProgress(0)
    setErrors([])

    try {
      const groupsData = divideLeadsIntoGroups()
      const totalGroups = groupsData.length
      const createdGroupsData = []

      for (let i = 0; i < totalGroups; i++) {
        const groupName = `${groupPrefix} ${i + 1}`
        const groupLeads = groupsData[i]

        // Simular criação do grupo (aqui seria a chamada real para API)
        await new Promise(resolve => setTimeout(resolve, 1000))

        // Simular sucesso/erro
        const success = Math.random() > 0.1 // 90% de sucesso

        if (success) {
          createdGroupsData.push({
            id: `group_${i + 1}`,
            name: groupName,
            leadsCount: groupLeads.length,
            status: 'created',
            leads: groupLeads
          })
        } else {
          setErrors(prev => [...prev, `Erro ao criar grupo: ${groupName}`])
        }

        setCreationProgress(((i + 1) / totalGroups) * 100)
      }

      setCreatedGroups(createdGroupsData)

      if (createdGroupsData.length > 0) {
        showSnackbar(`${createdGroupsData.length} ${langMessages['whatsapp.groups.success.groupsCreated']}`, 'success')
        handleNext()
      }

    } catch (error) {
      console.error('Erro ao criar grupos:', error)
      showSnackbar(langMessages['whatsapp.groups.error.createGroups'], 'error')
    } finally {
      setIsCreatingGroups(false)
    }
  }

  // Validações para cada step
  const isStepValid = (step) => {
    switch (step) {
      case 0:
        return selectedInstance !== ''
      case 1:
        return selectedLeads.length > 0
      case 2:
        return groupPrefix.trim() !== ''
      default:
        return true
    }
  }

  // Renderizar conteúdo do step
  const renderStepContent = (step) => {
    switch (step) {
      case 0:
        return (
          <Box>
            <Typography variant="body1" gutterBottom>
              {langMessages['whatsapp.groups.selectInstance.description']}
            </Typography>
            <FormControl variant="outlined" fullWidth style={{ marginTop: 16 }}>
              <InputLabel id="instance-label">
                {loadingInstances ? langMessages['whatsapp.groups.selectInstance.loading'] : langMessages['whatsapp.groups.selectInstance.label']}
              </InputLabel>
              <Select
                labelId="instance-label"
                value={selectedInstance}
                onChange={(e) => setSelectedInstance(e.target.value)}
                label={langMessages['whatsapp.groups.selectInstance.label']}
                disabled={loadingInstances}
              >
                {instances.map((instance) => (
                  <MenuItem key={instance.id} value={instance}>
                    <Box display="flex" alignItems="center" width="100%">
                      <Box flexGrow={1}>
                        {instance.title || instance.instanceName || 'Instância sem nome'}
                      </Box>
                      <Badge
                        color={instance.status?.state === 'open' ? 'success' : 'warning'}
                      >
                        {instance.status?.state === 'open' ? langMessages['whatsapp.groups.selectInstance.connected'] : langMessages['whatsapp.groups.selectInstance.disconnected']}
                      </Badge>
                    </Box>
                  </MenuItem>
                ))}
              </Select>
            </FormControl>
          </Box>
        )

      case 1:
        return (
          <Box>
            <Typography variant="body1" gutterBottom>
              {langMessages['whatsapp.groups.selectLeads.description']}
            </Typography>
            <Typography variant="body2" color="textSecondary" gutterBottom>
              {langMessages['whatsapp.groups.selectLeads.available']} {allLeads.length} | {langMessages['whatsapp.groups.selectLeads.selected']} {selectedLeads.length}
            </Typography>

            <RctCollapsibleCard
              fullBlock
              heading={
                <Typography variant="h6">
                  {langMessages['whatsapp.groups.selectLeads.title']} ({selectedLeads.length} selecionados)
                </Typography>
              }
            >
              <SimpleDualListBox
                autoHeightMin={300}
                autoHeightMax={500}
                filterInput
                choices={allLeads}
                values={selectedLeads}
                onChange={setSelectedLeads}
                isLoading={loadingLeads}
              />
            </RctCollapsibleCard>
          </Box>
        )

      case 2:
        const groupsNeeded = calculateGroupsNeeded()
        return (
          <Box>
            <Typography variant="body1" gutterBottom>
              {langMessages['whatsapp.groups.configureGroups.description']}
            </Typography>

            <TextField
              fullWidth
              variant="outlined"
              label={langMessages['whatsapp.groups.configureGroups.prefix.label']}
              value={groupPrefix}
              onChange={(e) => setGroupPrefix(e.target.value)}
              placeholder={langMessages['whatsapp.groups.configureGroups.prefix.placeholder']}
              style={{ marginTop: 16, marginBottom: 16 }}
              helperText={langMessages['whatsapp.groups.configureGroups.prefix.helper']}
            />

            <Box style={{ marginBottom: 16 }}>
              <GroupStats
                totalLeads={allLeads.length}
                selectedLeads={selectedLeads.length}
                groupsToCreate={groupsNeeded}
                maxLeadsPerGroup={MAX_LEADS_PER_GROUP}
                createdGroups={[]}
                isCreating={false}
              />
            </Box>

            <Card variant="outlined">
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  {langMessages['whatsapp.groups.configureGroups.summary.title']}
                </Typography>
                <Typography variant="body2">
                  <strong>{langMessages['whatsapp.groups.configureGroups.summary.instance']}</strong> {selectedInstance?.title || selectedInstance?.instanceName}
                </Typography>
              </CardContent>
            </Card>
          </Box>
        )

      case 3:
        return (
          <Box>
            <Typography variant="body1" gutterBottom>
              {langMessages['whatsapp.groups.createGroups.description']}
            </Typography>

            <Box style={{ marginTop: 16, marginBottom: 16 }}>
              <GroupStats
                totalLeads={allLeads.length}
                selectedLeads={selectedLeads.length}
                groupsToCreate={calculateGroupsNeeded()}
                maxLeadsPerGroup={MAX_LEADS_PER_GROUP}
                createdGroups={createdGroups}
                isCreating={isCreatingGroups}
              />
            </Box>

            {isCreatingGroups && (
              <Box style={{ marginTop: 16, marginBottom: 16 }}>
                <LinearProgress variant="determinate" value={creationProgress} />
                <Typography variant="body2" style={{ marginTop: 8 }}>
                  {langMessages['whatsapp.groups.createGroups.progress']} {Math.round(creationProgress)}%
                </Typography>
              </Box>
            )}

            {errors.length > 0 && (
              <Box style={{ marginTop: 16 }}>
                {errors.map((error, index) => (
                  <Alert key={index} severity="error" style={{ marginBottom: 8 }}>
                    {error}
                  </Alert>
                ))}
              </Box>
            )}

            {createdGroups.length > 0 && (
              <Box style={{ marginTop: 16 }}>
                <Typography variant="h6" gutterBottom>
                  {langMessages['whatsapp.groups.createGroups.created.title']} ({createdGroups.length})
                </Typography>
                {createdGroups.map((group) => (
                  <Card key={group.id} variant="outlined" style={{ marginBottom: 8 }}>
                    <CardContent>
                      <Box display="flex" justifyContent="space-between" alignItems="center">
                        <Typography variant="subtitle1">
                          {group.name}
                        </Typography>
                        <Chip
                          label={`${group.leadsCount} ${langMessages['whatsapp.groups.createGroups.leads']}`}
                          color="primary"
                          size="small"
                        />
                      </Box>
                    </CardContent>
                  </Card>
                ))}
              </Box>
            )}
          </Box>
        )

      default:
        return null
    }
  }

  return (
    <div className="dashboard-wrapper">
      <Paper style={{ padding: 24 }}>
        <Typography variant="h4" gutterBottom>
          {langMessages['whatsapp.groups.manager.title']}
        </Typography>
        <Typography variant="body1" color="textSecondary" gutterBottom>
          {langMessages['whatsapp.groups.manager.subtitle']}
        </Typography>

        <Divider style={{ margin: '24px 0' }} />

        <Stepper activeStep={activeStep} orientation="vertical">
          {STEPS.map((label, index) => (
            <Step key={label}>
              <StepLabel>{label}</StepLabel>
              <StepContent>
                {renderStepContent(index)}

                <Box style={{ marginTop: 16 }}>
                  <Button
                    disabled={activeStep === 0}
                    onClick={handleBack}
                    style={{ marginRight: 8 }}
                  >
                    {langMessages['whatsapp.groups.button.back']}
                  </Button>

                  {activeStep === STEPS.length - 1 ? (
                    <Box display="inline">
                      <Button
                        variant="contained"
                        color="primary"
                        onClick={createGroups}
                        disabled={isCreatingGroups || !isStepValid(activeStep)}
                        style={{ marginRight: 8 }}
                      >
                        {isCreatingGroups ? langMessages['whatsapp.groups.createGroups.creating'] : langMessages['whatsapp.groups.createGroups.create']}
                      </Button>
                      <Button onClick={handleReset}>
                        {langMessages['whatsapp.groups.createGroups.restart']}
                      </Button>
                    </Box>
                  ) : (
                    <Button
                      variant="contained"
                      color="primary"
                      onClick={handleNext}
                      disabled={!isStepValid(activeStep)}
                    >
                      {langMessages['whatsapp.groups.button.next']}
                    </Button>
                  )}
                </Box>
              </StepContent>
            </Step>
          ))}
        </Stepper>

        {activeStep === STEPS.length && (
          <Paper square elevation={0} style={{ padding: 24, marginTop: 16 }}>
            <Typography variant="h6" gutterBottom>
              {langMessages['whatsapp.groups.completed.title']}
            </Typography>
            <Typography variant="body1">
              {langMessages['whatsapp.groups.completed.description']}
            </Typography>
            <Button onClick={handleReset} style={{ marginTop: 16 }}>
              {langMessages['whatsapp.groups.completed.createNew']}
            </Button>
          </Paper>
        )}
      </Paper>
    </div>
  )
}

export default WhatsAppGroupManager
