import { Socket } from 'socket.io'
import { userById } from '../../libs/services/firestore/qiuser'

/**
 * Middleware function for socket rules.
 *
 * @param {Socket} socket - The socket object.
 * @param {Function} next - The next function to be called in the middleware chain.
 * @return {Promise<void>} - Resolves when the middleware is complete.
 */
export const SocketRulesMiddleware = async (socket: Socket, next: any) => {
  // Usuario obtido pelo SocketAuthMiddleware
  const user = (socket as any).user

  const { query } = socket.handshake
  const { userUid } = query

  // Id do usuario autenticado obtido pelo SocketAuthMiddleware
  const _authUserUid = String(user.uid)
  // Id do usuario recebido pelo Socket
  const _userUid = String(userUid)

  // Verifica se o usuario autenticado e o recebido sao iguais
  if (_authUserUid && _userUid) {
    // Busca o usuario pelo Id
    const qiUser = await userById(_authUserUid)

    // Verifica se o usuario existe
    if (qiUser && qiUser.accountId) {
      // Adiciona o accountId ao socket
      ;(socket as any).userAccountId = qiUser.accountId
      console.log('🤝 Socket Authorized User')

      // Chama o proximo middleware
      return next()
    }
  }

  // Chama o proximo middleware com erro
  console.log('🤝 Socket Unauthorized User')
  return next(new Error('unauthorized User'))
}
