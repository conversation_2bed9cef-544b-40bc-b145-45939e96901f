/* Dashboard Optimized Mode Styles */

.emergency-mode-header {
  background: linear-gradient(135deg, #2196f3 0%, #1976d2 100%);
  color: white;
  border: none;
  box-shadow: 0 4px 20px rgba(33, 150, 243, 0.3);
}

.emergency-mode-header .MuiCardContent-root {
  padding: 20px;
}

.emergency-mode-header h4 {
  color: white;
  font-weight: 600;
}

.emergency-mode-header .text-muted {
  color: rgba(255, 255, 255, 0.8) !important;
}

.emergency-mode-card {
  border: 2px dashed #e0e0e0;
  background: #fafafa;
  transition: all 0.3s ease;
}

.emergency-mode-card:hover {
  border-color: #2196f3;
  background: #f5f5f5;
  box-shadow: 0 2px 10px rgba(33, 150, 243, 0.1);
}

.emergency-mode-card .MuiCardContent-root {
  padding: 20px;
}

.emergency-mode-card h5 {
  color: #333;
  font-weight: 600;
}

.emergency-mode-card .text-muted {
  color: #666 !important;
  font-size: 14px;
}

.emergency-mode-card .MuiButton-root {
  min-width: 140px;
}

.emergency-mode-card .MuiLinearProgress-root {
  height: 3px;
  border-radius: 2px;
}

/* Animation for loading state */
.emergency-mode-card .MuiLinearProgress-root {
  animation: pulse 1.5s ease-in-out infinite;
}

@keyframes pulse {
  0% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
  100% {
    opacity: 1;
  }
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .emergency-mode-header .d-flex {
    flex-direction: column;
    align-items: flex-start !important;
  }

  .emergency-mode-header .MuiButton-root {
    margin-top: 15px;
    align-self: stretch;
  }

  .emergency-mode-card .d-flex {
    flex-direction: column;
    align-items: flex-start !important;
  }

  .emergency-mode-card .MuiButton-root {
    margin-top: 15px;
    align-self: stretch;
  }
}

/* Success state for loaded widgets */
.widget-loaded {
  animation: slideInUp 0.5s ease-out;
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
