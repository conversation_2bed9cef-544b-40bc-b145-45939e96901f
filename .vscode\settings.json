{"editor.codeActionsOnSave": {"source.fixAll.eslint": "always", "source.organizeImports": "always"}, "editor.defaultFormatter": "esbenp.prettier-vscode", "editor.formatOnSave": true, "editor.formatOnType": true, "editor.formatOnPaste": true, "editor.tabSize": 2, "[typescript]": {"editor.defaultFormatter": "vscode.typescript-language-features"}, "[html]": {"editor.defaultFormatter": "vscode.html-language-features"}, "[json]": {"editor.defaultFormatter": "vscode.json-language-features"}}