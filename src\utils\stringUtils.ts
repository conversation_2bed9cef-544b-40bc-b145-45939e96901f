const replaceSpace = (text: string, replaceWith: string) => {
  return text.replace(/[/\s/]/gim, replaceWith)
}

export const StringUtil = {
  replaceSpace,
}

/**
 * Sanitize Text
 * Remover caracteres especiais, tabulações ou espaços no inicio e fim do texto
 * @param text texto para ser sanitizado
 * @returns texto sanitizado
 */
export function sanitizeText(text: string): string {
  // Substituir quebras de linha e tabulações por espaços
  let sanitizedText = text.replace(/[\r\n\t]+/g, ' ');

  // Remover caracteres especiais que possam causar problemas no banco
  sanitizedText = sanitizedText.replace(/[^a-zA-Z0-9 ]/g, '');

  // Retornar o texto tratado
  return sanitizedText.trim();
}
