const AppModules = require('../../constants/AppModules')
const AppTaxonomies = require('../../constants/AppTaxonomies')

const pt_BR_Strings = {
  "accounts.account": "Conta",
  "accounts.accountSettings": "Configurações da Conta",
  "accounts.accountType": "Tipo de Conta",
  "accounts.brand": "Marca",
  "accounts.corporation": "Pessoa Jurídica",
  'accounts.corporation': 'Corporativa',
  "accounts.createAccountOny": "Criar Somente a Conta",
  "accounts.createSubscription": "Criar Assinatura",
  "accounts.createSubscriptionOnSave":
    "Deseja criar uma assinatura agora? Caso a conta possua um afiliado é informar o afiliado antes de criar a primeira assinatura",
  "accounts.hasPendingUpdate":
    "Sua conta possui um upgrade pendente. Realize o pagamento para ativá-lo",
  "accounts.inactiveAccount": "Inativa",
  "accounts.individual": "Pessoa Física",
  "accounts.invoice": "Nota Fiscal",
  "accounts.invoices": "Notas Fiscais",
  "accounts.mainUser": "Usuário principal da conta",
  "accounts.noLeadsOptions":
    "O plano não permite selecionar a quantidade de leads",
  "accounts.parentAccount": "Conta Matriz",
  "accounts.qtdLeads": "Quantidade de Leads",
  "accounts.settings": "Configurações",
  "accounts.slogan": "Slogan",
  "accounts.updateMyPlan": "Atualizar meu Plano",
  "accounts.updatePlan": "Atualizar o Plano",
  "accounts.updateSubscription": "Atualizar Assinatura",
  "accounts.upgrade": "Upgrade do Plano",
  "actions.add.brodcast": "Adicionar Mensagem em Massa",
  "actions.addConditions": "Adicionar Condições",
  "actions.addCustomField": "Criar Campo Personalizado",
  "actions.addDealTags": "Adicionar Tags ao Negócio",
  "actions.addField": "Adicionar Campo",
  "actions.addLeadTags": "Adicionar Tags ao Contato",
  "actions.addNewDeal": "Criar um Negócio",
  "actions.addScore": "Adicionar Scoring",
  "actions.addTags": "Adicionar Tags",
  "actions.addTasklist": "Adicionar uma lista de tarefas",
  "actions.addToCampaign": "Adicionar à Campanha",
  "actions.addToEvent": "Adicionar ao Evento",
  "actions.addToFunnel": "Adicionar ao Funil",
  "actions.addToSegmentation": "Adicionar à Segmentação",
  "actions.addToStore": "Adicionar à Loja",
  "actions.assign": "Designar",
  "actions.assignManager": "Designar Gerente de Contas",
  "actions.assignProfile": "Designar Perfil",
  "actions.assignSeller": "Designar Vendedor",
  "actions.assignTasklistToDeal": "Designar Tarefas",
  "actions.assignTasklistToTeam": "Designar Tarefas a uma Equipe",
  "actions.assignTasklistToUser": "Designar Tarefas a um Usuário",
  "actions.assignTeam": "Designar Equipe",
  "actions.assignUser": "Designar Usuário",
  "actions.back": "Voltar",
  "actions.changeFunnel": "Mudar de Funil",
  "actions.changeToFunnel": "Mudar para o funil",
  "actions.conditions": "Condições",
  "actions.continue": "Continuar",
  "actions.createEmbed": "Criar Embed",
  "actions.createModel": "Criar Modelo",
  "actions.createShortlink": "Criar Shortlink",
  "actions.createTicket": "Gerar um Pedido",
  "actions.dealWebhook": "Enviar Negócio por Webhook",
  "actions.deleteConditions": "Eliminar Condições",
  "actions.generateContract": "Gerar contrato",
  "actions.gotoAction": "Saltar a outra ação",
  "actions.gotoAutomation": "Iniciar outra automação",
  "actions.gotoReset": "Clique para redefinir a ação vinculada",
  "actions.gotoTooltip": "Arraste a mira até a ação desejada",
  "actions.importCustomField": "Importar Campo Personalizado",
  "actions.instructions.updateDeal":
    "Atualiza um Negócio já existente no Funil selecionado",
  "actions.manageAttachments": "Editar Anexos",
  "actions.newImport": "Nova Importação",
  "actions.other": "Outras Ações",
  "actions.preview": "Pré-visualizar",
  "actions.removeDealTags": "Eliminar Tags do Negócio",
  "actions.removeLeadTags": "Eliminar Tags do Contato",
  "actions.removeTags": "Eliminar Tags",
  "actions.saveConditions": "Salvar Condições",
  "actions.schedule": "Agendar",
  "actions.scheduled": "Agendado",
  "actions.selectContacTitle": "Selecione um contato",
  "actions.sendBroadcast": "Enviar Broadcast",
  "actions.sendContract": "Enviar contrato",
  "actions.sendEmail": "Enviar email",
  "actions.sendMessage": "Enviar mensagem",
  "actions.sendNotificationToManager": "Notificar Gerente de Contas",
  "actions.sendNotificationToSeller": "Notificar Vendedor",
  "actions.sendTestEmail": "Enviar Email de Teste",
  "actions.sendZap": "Zap QIPlus",
  "actions.sendZapToSeller": "Zap QIPlus - Vendedor",
  "actions.shotxSendMessageText": "Enviar mensagem de texto",
  "actions.stageBackInFunnel": "Progredir no Funil",
  "actions.stageForwardInFunnel": "Regredir no Funil",
  "actions.testEmail": "Teste de Envio",
  "actions.testImapUser": "Testar Configurações IMAP",
  "actions.testSMTPUser": "Testar Configurações SMTP",
  "actions.timer": "Timer",
  "actions.updateDeal": "Atualizar um Negócio",
  "actions.webhook": "Enviar Contato por Webhook",
  "affiliate.accountType.conta_corrente": "Conta Corrente",
  "affiliate.accountType.conta_corrente_conjunta": "CC Conjunta",
  "affiliate.accountType.conta_poupanca": "Conta Poupança",
  "affiliate.accountType.conta_poupanca_conjunta": "Poupança Conjunta",
  "affiliate.commission": "Comissão",
  "affiliate.commissions": "Comissões",
  "affiliate.data": "Dados do Afiliado",
  "affiliate.data.document_number": "Documento",
  "affiliate.data.email": "Email",
  "affiliate.data.name": "Nome Completo",
  "affiliate.data.phone_numbers": "Celular",
  "affiliate.data.site_url": "Site",
  "affiliate.data.type": "tipo de conta",
  "affiliate.termLink": "Link do Termo de Afiliação",
  "affiliate.transfer.auto": "Pagamento Automático",
  "affiliate.transfer.daily": "Diário",
  "affiliate.transfer.day": "Dia do Pagamento",
  "affiliate.transfer.interval": "Frequência de Pagamentos",
  "affiliate.transfer.monthly": "Mensal",
  "affiliate.transfer.weekly": "Semanal",
  "affiliates.deleteConfirmationBody":
    "O afiliado será eliminado permanentemente",
  "affiliates.deleteConfirmationTitle":
    "Tem certeza que deseja eliminar este afiliado?",
  "affiliates.duplicatedMsg": "Já existe um afiliado com estes dados",
  "affiliates.switchToPost": "Deseja editar o afiliado?",
  "alerts.accountWillBeSavedBefore":
    "Atenção, a conta será salva com as configurações atuais antes de realizar esta operação",
  "alerts.actionsWouldBeDeleted":
    "Existem ações na automação que seriam perdidas ao deletar esta etapa. Elimine as ações ou modifique a etapa a que pertencem antes de eliminar a etapa",
  "alerts.atention": "Atenção",
  "alerts.cardUpdateNotAllowed":
    "Não é possível alterar o cartão enquanto a assinatura está vigente",
  "alerts.changeModel":
    "Ao alterar o modelo o conteúdo editado será perdido. Deseja continuar?",
  "alerts.contentWasModifiedByUser":
    "O conteúdo que está sendo editado foi modificado por outro usuário",
  "alerts.contentWasModifiedInDB":
    "O conteúdo que está sendo editado foi modificado por outro usuário ou automação no banco de dados",
  "alerts.doingCheckout":
    "Estamos configurando sua conta. Por favor mantenha a janela do browser aberta até a conclusão do checkout.",
  "alerts.dontHaveData": "Não existem dados à serem exibidos",
  "alerts.emailAlreadyExists":
    "Já existe um usuário com o email informado. Utilize outro email ou recupere os dados da conta.",
  "alerts.emailChangeNotAllowed":
    "Não é permitida a alteração do email da conta",
  "alerts.emailExists": "Já existe um usuário com o email informado.",
  "alerts.emailSMTPSuccess":
    "As configurações de SMTP foram configuradas corretamente",
  "alerts.emailTestFailed": "Não foi possível enviar o email de teste",
  "alerts.emailTestSuccess": "Email de teste enviado com sucesso!",
  "alerts.emptyAccountOwner": "Informe o Usuário principal da conta",
  "alerts.emptyDisplayName": "Informe um nome de exibição",
  "alerts.emptyEmailAddress": "Informe um email",
  "alerts.emptyFirstName": "Informe um nome",
  "alerts.emptyRecipients": "Selecione destinatários",
  "alerts.emptyRoles": "Designe ao menos uma função ao usuário",
  "alerts.emptyStart": "Informe a data de início",
  "alerts.emptyTitle": "Informe um título",
  "alerts.imapUserAuthError": "Não foi possível estabelecer a conexão IMAP",
  "alerts.imapUserAuthSuccess": "Conexão IMAP estabelecida com sucesso",
  "alerts.informCountryCode": "Informe o código de país e o DDD",
  "alerts.informFullPhoneNumber": "Informe o telefone com código de país e DDD",
  "alerts.invalidPassword": "Senha inválida",
  "alerts.itemsLimitReached": "Limite de items atigindo",
  "alerts.mailboxNotSchedulable":
    "O envio através de caixas de entrada não permite agendamento futuro e/ou envios para toda a base de leads. O disparo será feito imediatamente e não constará na lista de envios",
  "alerts.minLengthPassord6": "A senha deve ser de ao menos 6 dígitos",
  "alerts.missingLoginData": "Usuário ou senha inválidos",
  "alerts.mobileViewportDetected":
    "Você está utilizando uma resolução de tela inferior à indicada para operar na plataforma. A resolução mínima sugerida é 1280x800",
  "alerts.noChangesToSave": "Não há modificações a serem salvas",
  "alerts.notAccountOwner": "O conteúdo não pertence à conta atual",
  "alerts.onChangeBillingData":
    "Ao modificar os dados de faturação, quando completar o checkout será gerada uma nova assinatura com data de início na data atual.",
  "alerts.onlyUpgradeAllowed":
    "O valor do novo plano deve ser maior ao do plano atual para realizar o checkout de uma assinatura ativa",
  "alerts.operationWillBeAborted":
    "A operação em curso será perdida e não poderá ser recuperada posteriormente",
  "alerts.phoneAlreadyExists":
    "Já existe um usuário com o telefone informado. Utilize outro telefone ou recupere os dados da conta.",
  "alerts.plaeaseRelateFields":
    "Por favor, selecione os campos para associação",
  "alerts.pleaseCompletePersonalInfo": "Por favor, complete seu registro",
  "alerts.pleaseInformValidCPF": "Por favor, informe um CPF válido",
  "alerts.pleaseInformYourCPF": "Por favor, informe seu CPF",
  "alerts.pleaseInformYourDisplayName":
    "Por favor, informe seu Nome de Usuário",
  "alerts.pleaseInformYourEmail": "Por favor, informe seu email",
  "alerts.pleaseInformYourEmailAndPassword": "Por favor, informe email e senha",
  "alerts.pleaseInformYourMobileNumber":
    "Por favor, informe o número de celular",
  "alerts.pleaseInformYourPassword": "Por favor, informe sua senha",
  "alerts.resetViewsConfirm":
    "Isto irá zerar as visualizações contabilizadas até o momento. Tem certeza que deseja continuar?",
  "alerts.subscriptionWilBeUpdated":
    "Ao realizar o checkout sua conta será atualizada e será cobrado apenas o valor proporcional do novo plano para o tempo restante da assinatura, sem implicar a modificação da data de renovação ou de valores cobrados anteriormente.",
  "alerts.tooManyRequests":
    "Você ultrapassou o número máximo de tentativas de login. Tente novamente mais tarde",
  "alerts.updateToBoletoNotAllowed":
    "Não é possível alterar a forma de pagamento de boleto para cartão",
  "alerts.userAlreadyExists":
    "Já existe um usuário com os dados informados. Crie um novo usuário ou recupere os dados da conta.",
  "alerts.userIsNotActive":
    "Ops, parece que seu usuário não se encontra ativo no momento",
  "alerts.userNotRegistered":
    "Você ainda não possui um usuário ativo no QI Plus. Registre-se agora ou comunique-se conosco para conhecer mais sobre o QIPlus.",
  "alerts.validateYourEmailAddress": "Valide o seu email antes de proseguir",
  "alerts.verifyFormErrors":
    "Verifique os erros do formulário antes de proseguir",
  "alerts.weakPassword": "Escolha uma senha com no mínimo 6 caracteres",
  "alerts.welcomeToAffiliateProgram":
    "Bem-vindo ao programa de afiliados do QIPlus",
  "alerts.wrongPassword": "Senha inválida",
  "alerts.youMightLooseYourChanges":
    "Algumas informações que estão sendo editadas podem ser perdidas por modificações externas",
  "app.appName": "QIPlus",
  "app.slogan": "Transforme seus clientes em fãs",
  "app.sloganAction": "Comece agora a transformar seus clientes em fãs!",
  "app.updatedMsg": "Solicitação processada com sucesso!",
  "app.welcome": "Bem-vindo ao QI Plus!",
  "ask.displayName": "Como prefere ser chamado?",
  "attachments.download": "Fazer download",
  "attachments.downloadAll": "Fazer download de todos os anexos",
  "automations.fired": "Disparos",
  "automations.lastActionAlert":
    "Esta ação somente pode ser adicionada ao final de uma sequência de ações",
  "automations.loopAlert":
    "Não é possível adicionar o mesmo item nos disparadores e na automação.",
  "automations.possibleLoopDanger":
    "Esta ação não pode ser realizada pois poderia gerar um loop infinito.",
  "button.4Steps": "Reveja os 4 passos para iniciar sua operação de vendas",
  "button.accept": "Aceitar",
  "button.acceptTerms": "Aceitar Termos",
  "button.add": "Adicionar",
  "button.addNew": "Novo",
  "button.addNewUser": "Adicionar Usuário",
  "button.addOption": "Adicionar Opção",
  "button.addOptions": "Adicionar Opções",
  "button.addTriggerGroup": "Novo conjunto de regras",
  "button.assignNow": "Assign Now",
  "button.associate": "Associar ao Lead",
  "button.back": "Voltar",
  "button.blockLevelButton": "Block Level Button",
  "button.button": "Botão",
  "button.cancel": "Cancelar",
  "button.click": "Clique",
  "button.clone": "Clonar",
  "button.close": "Fechar",
  "button.complete": "Finalizar",
  "button.completeStep": "Complete Step",
  "button.confirm": "Confirmar",
  "button.connect": "Conectar",
  "button.convert": "Converter",
  "button.copy": "Copiar",
  "button.create": "Criar",
  "button.createNewLead": "Criar novo Lead",
  "button.cropImage": "Cortar Image",
  "button.danger": "Danger",
  "button.delete": "Eliminar",
  "button.discard": "Descartar",
  "button.disconnectAccount": "Desconectar da conta",
  "button.downloadPdfReport": "Download Pdf Report",
  "button.edit": "Editar",
  "button.editOption": "Editar Opção",
  "button.editOptions": "Editar Opções",
  "button.editTicket": "Modificar Pedido",
  "button.error": "Erro",
  "button.exclude": "Excluir",
  "button.exportToExcel": "Exportar a Excel",
  "button.goToCampaign": "Go To Campaign",
  "button.hide": "Ocultar",
  "button.import": "Importar",
  "button.info": "Info",
  "button.largeButton": "Large Button",
  "button.learnMore": "Ver Mais",
  "button.leave": "Sair",
  "button.like": "Like",
  "button.link": "Link",
  "button.lost": "Perdido",
  "button.more": "Mais",
  "button.newLead": "Novo Lead ",
  "button.newOption": "Nova Opção",
  "button.newOptions": "Novas Opções",
  "button.next": "Próximo",
  "button.nfConfirm": "Emitir Nota Fiscal",
  "button.nfConfirmNotazz": "Emitir Nota Fiscal em Notazz",
  "button.no": "Não",
  "button.ok": "Ok",
  "button.openMenu": "Abrir Menu",
  "button.openPopover": "Open Popover",
  "button.openWithFadeTransition": "Open With Fade Transition",
  "button.pen": "Pen",
  "button.preview": "Preview",
  "button.primary": "Primary",
  "button.primaryButton": "Primary Button",
  "button.reassignUsers": "Designar Conta",
  "button.reject": "Descartar",
  "button.remove": "Eliminar",
  "button.removeAll": "Eliminar Todos",
  "button.removeTriggerGroup": "Eliminar conjunto de regras",
  "button.reply": "Responder",
  "button.reset": "Limpar",
  "button.resetField": "Limpar Campo",
  "button.resetFilters": "Limpar Filtros",
  "button.resetStyles": "Restabelecer Estilos",
  "button.resetViews": "Zerar Visualizações",
  "button.save": "Salvar",
  "button.saveAndContinue": "Salvar e continuar editando",
  "button.saveAsDraft": "Salvar como Rascunho",
  "button.saveChanges": "Salvar Modificações",
  "button.saveDraft": "Salvar Rascunho",
  "button.saveNow": "Salvar Agora",
  "button.search": "Pesquisar",
  "button.secondary": "Secondary",
  "button.seeInsights": "See Insights",
  "button.select": "Selecione",
  "button.send": "Enviar",
  "button.sendMessage": "Enviar Mensagem",
  "button.sendTicket": "Finalizar Pedido",
  "button.settings": "Configurar",
  "button.show": "Mostrar",
  "button.signIn": "Acessar",
  "button.signUp": "Criar uma Conta",
  "button.smallButton": "Small Button",
  "button.success": "Successo",
  "button.support": "Abrir chamado de suporte",
  "button.tryAgain": "Tentar Novamente",
  "button.understood": "Entendido",
  "button.undo": "Desfazer",
  "button.undoExternalChanges": "Desfazer Modificações Externas",
  "button.update": "Atualizar",
  "button.useDefaultImage": "Use Default Image",
  "button.view": "Ver",
  "button.viewAll": "Ver Todos",
  "button.viewLess": "Ver Menos",
  "button.viewMore": "Ver Mais",
  "button.viewProfile": "Ver Perfil",
  "button.warning": "Alerta",
  "button.won": "Ganho",
  "button.writeNewMessage": "Nova Mensagem",
  "button.yes": "Sim",
  "calendar.agenda": "Agenda",
  "calendar.agendas": "Agendas",
  "calendar.allDay": "Todo o dia",
  "calendar.date": "Data",
  "calendar.dates": "Datas",
  "calendar.day": "Dia",
  "calendar.days": "Dias",
  "calendar.deleteConfirmationBody": "O evento será eliminado permanentemente",
  "calendar.deleteConfirmationTitle":
    "Tem certeza que deseja eliminar este evento?",
  "calendar.event": "Evento",
  "calendar.hour": "Hora",
  "calendar.hours": "Horas",
  "calendar.info": "Dados do Evento",
  "calendar.minute": "Minuto",
  "calendar.minutes": "Minutos",
  "calendar.month": "Mês",
  "calendar.month.abr": "Abril",
  "calendar.month.ago": "Agosto",
  "calendar.month.dez": "Dezembro",
  "calendar.month.fev": "Fevereiro",
  "calendar.month.jan": "Janeiro",
  "calendar.month.jul": "Julho",
  "calendar.month.jun": "Junho",
  "calendar.month.mai": "Maio",
  "calendar.month.mar": "Março",
  "calendar.month.nov": "Novembro",
  "calendar.month.out": "Outubro",
  "calendar.month.set": "Setembro",
  "calendar.months": "Meses",
  "calendar.newEvent": "Novo Evento",
  "calendar.next": "Seguinte",
  "calendar.previous": "Anterior",
  "calendar.second": "Segundo",
  "calendar.seconds": "Segundos",
  "calendar.time": "Horário",
  "calendar.today": "Hoje",
  "calendar.tomorrow": "Amanhã",
  "calendar.updateEvent": "Editar Evento",
  "calendar.week": "Semana",
  "calendar.weeks": "Semanas",
  "calendar.work_week": "Dias Úteis",
  "calendar.year": "Ano",
  "calendar.years": "Anos",
  "calendar.yesterday": "Ontem",
  "campaigns.campaignHomeUrl": "URL principal da campanha",
  "campaigns.hunter.actions": "Ações",
  "campaigns.hunter.analytics.clickDetails.byElement": "Cliques por Elemento",
  "campaigns.hunter.analytics.clickDetails.byElementDescription":
    "Selecione um elemento para ver os detalhes de cliques",
  "campaigns.hunter.analytics.clickDetails.byElementId":
    "Cliques por ID em :element",
  "campaigns.hunter.analytics.clickDetails.title": "Detalhes de cliques",
  "campaigns.hunter.analytics.eventsEvolution.title": "Evolução dos eventos",
  "campaigns.hunter.analytics.filters": "Filtros",
  "campaigns.hunter.analytics.filters.byQueryParamName":
    "Filtrar por Nome do Parâmetro",
  "campaigns.hunter.analytics.filters.byQueryParamValue":
    "Filtrar por Valor do Parâmetro",
  "campaigns.hunter.analytics.filters.byRoute": "Filtrar por Rota",
  "campaigns.hunter.analytics.filters.byRouteTooltip":
    "Filtre pelas rotas que tiveram acesso durante o período selecionado",
  "campaigns.hunter.analytics.filters.clear": "Limpar",
  "campaigns.hunter.analytics.filters.loading": "Carregando...",
  "campaigns.hunter.analytics.filters.noOptions": "Nenhum filtro disponível",
  "campaigns.hunter.analytics.filters.open": "Abrir",
  "campaigns.hunter.analytics.noClicks": "Nenhum clique registrado",
  "campaigns.hunter.analytics.noData": "Nenhum dado para exibir",
  "campaigns.hunter.analytics.title": "Métricas do QI Hunter",
  "campaigns.hunter.cancel": "Cancelar",
  "campaigns.hunter.clone": "Cópia",
  "campaigns.hunter.close": "Fechar",
  "campaigns.hunter.code": "Código do QI Hunter",
  "campaigns.hunter.codeEnd": "Fim do QI Hunter",
  "campaigns.hunter.codeStart": "Inicio do QI Hunter",
  "campaigns.hunter.copied": "Copiado para a area de transferência",
  "campaigns.hunter.copy": "Copiar",
  "campaigns.hunter.create": "Criar QI Hunter",
  "campaigns.hunter.created": "QI Hunter criado com sucesso",
  "campaigns.hunter.createdAt": "Criado em",
  "campaigns.hunter.createFailed": "Falha ao criar QI Hunter",
  "campaigns.hunter.creating": "Criando QI Hunter",
  "campaigns.hunter.delete": "Excluir",
  "campaigns.hunter.description": "Crie e gerencie seus pixels de rastreamento",
  "campaigns.hunter.domain": "Dominio",
  "campaigns.hunter.duplicate": "Duplicar",
  "campaigns.hunter.edit": "Editar QI Hunter",
  "campaigns.hunter.events.clicked": "Cliques",
  "campaigns.hunter.events.empty":
    "Nenhum evento selecionado para rastreamento",
  "campaigns.hunter.events.select": "Eventos para rastrear",
  "campaigns.hunter.events.title": "Eventos",
  "campaigns.hunter.events.viewed": "Visualizações",
  "campaigns.hunter.events.warning":
    "Copie e atualize o código do QI Hunter para que as alterações sejam refletidas.",
  "campaigns.hunter.gridView": "Visualização de Grade",
  "campaigns.hunter.listView": "Visualização de Lista",
  "campaigns.hunter.name": "Nome do QI Hunter",
  "campaigns.hunter.new": "Novo QI Hunter",
  "campaigns.hunter.newDescription": "Descrição do QI Hunter",
  "campaigns.hunter.stats": "Estatísticas",
  "campaigns.hunter.status": "Status",
  "campaigns.hunter.statusDisabled": "Desativado",
  "campaigns.hunter.statusEnabled": "Ativado",
  "campaigns.hunter.statusToggle": "Ativar/Desativar",
  "campaigns.hunter.tags": "Tags",
  "campaigns.hunter.title": "QI Hunter",
  "campaigns.hunter.update": "Atualizar QI Hunter",
  "campaigns.hunter.updated": "QI Hunter atualizado com sucesso",
  "campaigns.hunter.updatedAt": "Atualizado em",
  "campaigns.hunter.updateFailed": "Falha ao atualizar QI Hunter",
  "campaigns.performance": "Performance de Campanhas",
  "cart.deleteWarning": "As modificações no Pedido serão perdidas",
  "chat.contactInfo": "Info do contato",
  "chat.deleteGroup": "Eliminar grupo",
  "chat.filterContacts": "Filtrar Contatos",
  "chat.group": "Grupos",
  "chat.leaveChat": "Eliminar conversa",
  "chat.leaveGroup": "Sair do grupo",
  "chat.newGroup": "Novo grupo",
  "chat.noContactsSelected": "Nenhum contato selecionado",
  "chat.renameGroup": "Renomear grupo",
  "chat.selectContacts": "Selecionar Contatos",
  "chat.sendToMultipleRecipients": "Enviar para vários destinatários",
  "chat.startChatTip":
    "Escolha um contato ou usuário para iniciar uma conversa",
  "chat.startChatTip2": "Teste",
  "chat.typeAContactName": "Digite o nome do contato",
  "chat.typeAGroupName": "Digite o nome do grupo",
  "chat.typeAMessage": "Escreva uma mensagem",
  "chat.writeAMessage": "Escreva sua mensagem",
  "checklist.add": "Nova Checklist",
  "checklist.clone": "Clonar Checklist",
  "checklist.create": "Criar Checklist",
  "checklist.edit": "Editar Checklist",
  "checklists.clone": "Clonar Checklists",
  "checklists.edit": "Editar Checklists",
  "commissions.canceled": "Cancelada",
  "commissions.ended": "Finalizada",
  "commissions.paid": "Paga",
  "commissions.pending_payment": "Pendente",
  "commissions.trialing": "Trial",
  "commissions.unpaid": "Não paga",
  "components.addNewTasks": "Nova Tarefa",
  "components.address": "Endereço",
  "components.address2Optional": "Endereço 2 (Opcional)",
  "components.addToCart": "Add To Cart",
  "components.advancedSettings": "Configurações Avançadas",
  "components.ageRange": "Intervalo de idade",
  "components.all": "Todos",
  "components.approve": "Aprovar",
  "components.automation": "Automação",
  "components.automations": "Automações",
  "components.basic": "Basic",
  "components.basicChip": "Basic Chip",
  "components.basicFeatures": "Características Básicas",
  "components.billingAddress": "Endereço de Faturação",
  "components.billingData": "Dados de Faturação",
  "components.buyNow": "Buy Now",
  "components.cancelled": "Cancelado",
  "components.cardNumber": "Número do Cartão",
  "components.cart": "Carro de compras",
  "components.CartEmptyText": "O carro de compras está vazio",
  "components.changeBillingData": "Modificar Dados de Faturação",
  "components.checkout": "Checkout",
  "components.choose": "Escolher",
  "components.city": "Cidade",
  "components.clickableChip": "Clickable Chip",
  "components.companyName": "Nome da Empresa",
  "components.compose": "Compose",
  "components.confirmPasswords": "Confirmar Senha",
  "components.country": "País",
  "components.customIcon": "Custom Icon",
  "components.customStyle": "Custom Style",
  "components.cvv": "CV",
  "components.data": "Dados",
  "components.do": "Do",
  "components.done": "Done",
  "components.dontHaveAccountSignUp": "Dont Have Account SignUp",
  "components.drafts": "Rascunhos",
  "components.email": "Email",
  "components.emailPrefrences": "Preferências de Email",
  "components.enterEmailAddress": "Enter Email Address",
  "components.enterUserName": "Enter User Name",
  "components.expansionPanel1": "Expansion Panel 1",
  "components.expansionPanel2": "Expansion Panel 2",
  "components.expiryDate": "Vencimento",
  "components.facebookAds": "Facebook Ads",
  "components.facebookReports": "Relatórios do Facebook",
  "components.firstName": "Nome",
  "components.followers": "Seguidores",
  "components.funnelActions": "Ações",
  "components.generalSetting": "Configurações Básicas",
  "components.goToHomePage": "Go To Home Page",
  "components.goToShop": "Go To Shop",
  "components.integration": "Integração",
  "components.integrations": "Integrações",
  "components.item": "Item",
  "components.items": "Itens",
  "components.last1Month": "Último Mês",
  "components.last6Month": "Últimos 6 Meses",
  "components.last7Days": "Últimos 7 Dias",
  "components.lastName": "Sobrenome",
  "components.leadEntrances": "Entrada de Leads",
  "components.left": "Esquerda",
  "components.mobileNumber": "Celular",
  "components.month": "Mês",
  "components.myProfile": "Meu perfil",
  "components.nameOnCard": "Nome no Cartão",
  "components.NoItemFound": "Nenhum item encontrado",
  "components.occupation": "Profissão",
  "components.openAlert": "Open Alert",
  "components.openFullScreenDialog": "Open Full Screen Dialogs",
  "components.pageNotfound": "Page not Found",
  "components.paid": "Pago",
  "components.passwordPrompt": "Password Prompt",
  "components.passwords": "Passwords",
  "components.payment": "Pagamento",
  "components.payNow": "Pay Now",
  "components.pending": "Pendente",
  "components.persistentDrawer": "Persistent Drawer",
  "components.phoneNo": "Telefone",
  "components.pipeline": "Pipeline",
  "components.pipelines": "Pipelines",
  "components.placeOrder": "Place Order",
  "components.popularity": "Popularidade",
  "components.print": "Imprimir",
  "components.product": "Produto",
  "components.projectName": "Nome do Projeto",
  "components.prompt": "Prompt",
  "components.quantity": "Quantidade",
  "components.refunded": "Reembolsado",
  "components.removeProduct": "Eliminar Produto",
  "components.right": "Direita",
  "components.saveContinue": "Salvar e Continuar",
  "components.sendMessage": "Send Message",
  "components.sent": "Enviados",
  "components.settings": "Configurações",
  "components.ShippingAddressText":
    "Shipping address is the same as billing address.",
  "components.signIn": "Acessar",
  "components.slideInAlertDialog": "Slide In Alert Dialog",
  "components.social Connection": "Social Connection",
  "components.sorryServerGoesWrong": "Sorry Server Goes Wrong",
  "components.spaceUsed": "Space Used",
  "components.state": "Estado",
  "components.submit": "Submit",
  "components.success": "Success",
  "components.summary": "Summary",
  "components.task_question": "Questionario",
  "components.tasklist": "Tarefa",
  "components.tasklists": "Tarefas",
  "components.title": "Título",
  "components.today": "Hoje",
  "components.totalPrice": "Preço Total",
  "components.trash": "Lixeira",
  "components.trending": "Trending",
  "components.unlock": "Unlock",
  "components.username": "Username",
  "components.viewCart": "Ver Carro de compras",
  "components.warning": "Warning",
  "components.week": "Semana",
  "components.withDescription": "With Description",
  "components.withHtml": "With Html",
  "components.year": "Ano",
  "components.yesterday": "Ontem,",
  "components.zip": "CEP",
  "components.zipCode": "CEP",
  "config.append_questionnaires": "Anexar questionários?",
  "config.checkin": "Realiza Check-in",
  "config.confirm": "Confirmação de Participantes",
  "config.confirmation": "Confirmação de Participantes",
  "config.custom_fields": "Deseja utilizar campos personalizados?",
  "config.customDomain": "Domínio Personalizado",
  "config.event_check": "Deseja associar a um evento ou campanha?",
  "config.hide_header": "Esconder o cabeçalho da Loja",
  "config.hide_login": "Esconder a barra de login QIPlus",
  "config.hide_menu": "Esconder o menu do Site",
  "config.integrated": "Integrar com outra plataforma",
  "config.liveqiplus": "Integrar ao Live QIPlus",
  "config.managers_mode": "Modo de Distribuição",
  "config.model_check": "Preencher a partir de um modelo?",
  "config.notification": "Notificação",
  "config.notifications": "Notificações",
  "config.participants_score": "Scoring por Participação",
  "config.prevent_dup_ticket":
    "Impedir que a automacao seja percorrida mais de uma vez em um mesmo ticket ",
  "config.prevent_dup_user":
    "Impedir que a automacao seja percorrida mais de uma vez pelo mesmo usuário",
  "config.questionnaire": "Possui Questionário",
  "config.raffle": "Realiza Sorteios",
  "config.raffleType": "Tipo de Sorteio",
  "config.redirect": "Redirecionar Leads a outro destino após o envio?",
  "config.repeat":
    "Permitir que o email seja enviado mais de uma vez ao mesmo contato",
  "config.sales_mode": "Modalidade de Vendas",
  "config.score": "Soma scoring por participação",
  "config.sellers_mode": "Modo de Distribuição",
  "config.SMTPSettings": "Configuração SMTP",
  "config.stock_check": "Controlar Estoque",
  "config.use_as_template": "Utilizar como modelo",
  "contact.add": "Novo contato",
  "contact.clone": "Clonar contato",
  "contact.create": "Criar contato",
  "contact.edit": "Editar contato",
  "contacts.clone": "Clonar contatos",
  "contacts.edit": "Editar contatos",
  "contracts.defaultSubject": "QIPlus || Contrato",
  "contracts.repeat":
    "Permitir que o contrato seja enviado mais de uma vez ao mesmo contato",
  'createdAt': 'Created At',
  "customers.selectOneCustomer": "Selecione um cliente",
  "date.showGroupFormat": "dddd, DD [de] MMMM [de] YYYY",
  "date.sortFullDate": "YYYYMMDDHHmmss",
  "date.sortGroupFormat": "YYYYMMDD",
  "dates.after": "Após",
  "dates.before": "Antes de",
  "dates.between": "Entre",
  "dates.currentMonth": "Mês Corrente",
  "dates.currentWeek": "Semana Corrente",
  "dates.currentYear": "Ano Corrente",
  "dates.custom": "Personalizado",
  "dates.dateFormat": "Formato",
  "dates.from": "De",
  "dates.fromAlt": "Desde",
  "dates.last_28d": "Ultimos 28 dias",
  "dates.last_3d": "Ultimos 3 dias",
  "dates.last_7d": "Ultimos 7 dias",
  "dates.last_90d": "Ultimos 90 dias",
  "dates.last_month": "Ultimo Mês",
  "dates.last_week_sun_sat": "Ultima Semana",
  "dates.last_year": "Ultimo Ano",
  "dates.last15Days": "Últimos 15 Dias",
  "dates.last30Days": "Últimos 30 Dias",
  "dates.last365Days": "Últimos 365 Dias",
  "dates.last7Days": "Últimos 7 Dias",
  "dates.last90Days": "Últimos 90 Dias",
  "dates.modified": "Modificado em",
  "dates.period": "Período",
  "dates.periodicity": "Periodicidade",
  "dates.this_month": "Este Mês",
  "dates.this_week_sun_today": "Esse Semana",
  "dates.this_year": "Este Ano",
  "dates.today": "Hoje",
  "dates.until": "Até",
  "dates.yesterday": "Ontem",
  "deals.createFunnelBeforeEdit":
    "Crie ao menos um funil para associar as negociações",
  "deals.info": "Dados do Negócio",
  "deals.newContacTitle": "Contato",
  "deals.sentTo.lost": "Negócio encerrado como perdido",
  "deals.sentTo.won": "Negócio encerrado como ganho",
  "deals.setNewContactData":
    "Um contato foi encontrado no banco de dados com os dados inseridos. Deseja importá-lo?",
  "domains.DNSInstructions":
    "Configure os servidores DNS em seu provedor de domínio com os seguintes valores:",
  "domains.subdomainInstructions":
    "Para configurar um subdomínio, utilize este endereço IP:",
  "email.content": "Corpo do Email",
  "email.defaultSubject": "QIPlus || Email",
  "email.error": "Erro",
  "email.fields.address": "Endereço",
  "email.fields.bcc": "cco",
  "email.fields.cc": "cc",
  "email.fields.content": "Corpo do Email",
  "email.fields.forward": "Reencaminhar a",
  "email.fields.from": "de",
  "email.fields.fromEmail": "Email do Remetente",
  "email.fields.fromName": "Nome do Remetente",
  "email.fields.host": "Host",
  "email.fields.html": "Mensagem",
  "email.fields.password": "Senha",
  "email.fields.port": "Porta",
  "email.fields.provider": "Provedor de Emails",
  "email.fields.signature": "Assinatura",
  "email.fields.subject": "Assunto",
  "email.fields.tls": "TLS (Encriptação de segurança)",
  "email.fields.to": "para",
  "email.fields.user": "Usuário (email)",
  "email.head.bcc": "cco",
  "email.head.cc": "cc",
  "email.head.from": "de",
  "email.head.subject": "Assunto",
  "email.head.to": "para",
  "email.pending": "Pendente",
  "email.processing": "Processando",
  "email.scheduled": "Agendado",
  "email.scheduledError": "Email não pôde ser agendado",
  "email.scheduledSuccess": "Email agendado com sucesso",
  "email.sending": "Enviando",
  "email.sent": "Enviado",
  "email.sentError": "Email não pôde ser enviado",
  "email.sentSuccess": "Email enviado com sucesso",
  "email.test": "Teste",
  "email.testSent": "Teste Enviado",
  "email.unknown": "Desconhecido",
  "emails.attachment": "Anexo",
  "emails.attachments": "Anexos",
  "emails.bcc": "CCO",
  "emails.broadcast": "Enviar para toda a base de leads",
  "emails.cc": "CC",
  "emails.defaultFooter": "Rodapé padrão do email",
  "emails.defaultFooterTip": "Pode ser substituido na tela de edição do email",
  "emails.defaultSendMethod": "Método de envio padrão",
  "emails.deleteAllScheduledMail": "Eliminar todos os envios agendados?",
  "emails.emailTestSuccess": "Teste enviado com sucesso",
  "emails.footer": "Rodapé do email",
  "emails.footerAddress": "Endereço no rodapé do email",
  "emails.footerAddressExample":
    "Ex: QIPlus Sistemas. Rua Adélia de Oliveira, 30. Residencial Pacaembu, Itupeva – SP. CEP 13295-000. Brasil",
  "emails.footerAddressTip":
    "Insira o seu endereço completo, em conformidade com a Lei Geral de Proteção de Dados (LGPD).",
  "emails.from": "Remetente",
  "emails.fromName": "Nome do Remetente",
  "emails.markAsSpam": "Marcar como spam",
  "emails.markedAsSpam": "Emails marcados como spam",
  "emails.markedAsSpam.singular": "Email marcado como spam",
  "emails.minDateMessage":
    "A data e horário de envio devem ser maiores do que a data atual",
  "emails.movedMsg": "Emails movidos com sucesso",
  "emails.movedMsg.singular": "Email movido com sucesso",
  "emails.moveMsg": "Mover Emails",
  "emails.moveMsg.singular": "Mover Email",
  "emails.recipient": "Destinatário",
  "emails.recipients": "Destinatários",
  "emails.repeat":
    "Permitir que o email seja enviado mais de uma vez ao mesmo contato",
  "emails.scheduleBroadcast":
    "Tem certeza que deseja enviar para toda a sua base de leads?",
  "emails.scheduled_date": "Data do Envio",
  "emails.scheduledSuccess": "Emails agendados com sucesso",
  "emails.scheduleEmail": "Agendar envio",
  "emails.scheduleNewMail": "Deseja programar o disparo do email agora?",
  "emails.selectMailbox": "Selecione uma Caixa de Entrada",
  "emails.sendingOptions": "Opções de Envio",
  "emails.sendMethod": "Método de envio",
  "emails.sendNewMail": "Deseja efetuar o disparo do email agora?",
  "emails.sentError": "Emails não puderam ser enviados",
  "emails.sentSuccess": "Emails enviados com sucesso",
  "emails.signinProvider": "Faça login com seu provedor",
  "emails.signinProviderAndTyAgain":
    "Faça login com seu provedor e tente novamente",
  "emails.subject": "Assunto",
  "errors.birthMonth": "Por favor selecione um mês",
  "errors.chooseFieldType": "Selecione o tipo de campo",
  "errors.createChoicesBeforeAddingField": "Crie opções para o campo",
  "errors.dbErrorMsg":
    "Ocorreu um erro na conexão ao banco de dados. Tente novamente",
  "errors.emptyInput": "Preencha todos os campos",
  "errors.errorCode": "Código do erro",
  "errors.errorFetchingPosts":
    "Ocorreu um erro ao carregar os items desta seção. Tente novamente ou comunique-se com o nosso suporte se o erro persistir",
  "errors.failedValidation": "Corrija os erros no formulário",
  "errors.fetchErrorMsg": "Ocorreu um erro na requisição. Tente novamente",
  "errors.gender": "Por favor selecione um gênero",
  "errors.genericErrorMsg":
    "Ocorreu um erro inesperado. Tente novamente ou comunique-se com o nosso suporte se o erro persistir",
  "errors.inexistsErrorMsg": "O documento não existe no banco de dados",
  "errors.integrationErrorMsg":
    "Ocorreu um erro na integração. Tente novamente ou comunique-se com o nosso suporte se o erro persistir",
  "errors.invalidCNPJ": "Número de CNPJ inválido",
  "errors.invalidCPF": "Número de CPF inválido",
  "errors.invalidEmailAddress": "O email informado não é válido",
  "errors.invalidForwardAddress": "O email para redirecionamento não é válido",
  "errors.invalidMonth": "O mês informado é invalido",
  "errors.invalidRecipients": "Destinatário inválido",
  "errors.invalidZipCode": "CEP inválido",
  "errors.loopDanger":
    "Esta ação não pode ser realizada pois gera um loop infinito.",
  "errors.MODULE_LIMIT_REACHED":
    "Você atingiu o limite de items deste módulo do QIPlus. Faça um upgrade da sua conta para aumentar o limite",
  "errors.MODULE_NOT_AVAILABLE":
    "Este módulo não está habilitado. Faça um upgrade da sua conta para acessá-lo",
  "errors.permissionDenied":
    "Seu usuário não conta com a permissão necessária para esta operação",
  "errors.sessionExpired":
    "Sua sessão de autenticação expirou, faça login novamente",
  "errors.setFieldLabel": "Insira um rótulo para o campo",
  "errors.userHasBeenRemoved": "O usuário não existe ou não se encontra ativo",
  "errors.yearCantBeLowerThanCurrent": "O ano deve ser maior ou igual ao atual",
  "errors.invalidCustomToken": "Token inválido ou expirado",
  "events.participants": "Participantes do Evento",
  "facebook.facebookRecomendation":
    "Recomendamos que efetue o login com a conta empresarial, para que não haja conflitos",
  "facebook.logoutAlert":
    "Caso você deslogue, perderá todas as integrações conectadas com esta conta do Facebook, até que se conecte novamente ao app QIPlus",
  "facebookcapi.access_token": "Token de Acesso",
  "facebookcapi.AddPaymentInfo": "Adicionar dados de pagamento",
  "facebookcapi.AddToCart": "Adicionar ao carrinho",
  "facebookcapi.AddToWishlist": "Adicionar à lista de desejos",
  "facebookcapi.CompleteRegistration": "Concluir registro",
  "facebookcapi.Contact": "Entrar em contato",
  "facebookcapi.customEvent": "Evento personalizado",
  "facebookcapi.customEventName": "Nome do evento personalizado",
  "facebookcapi.CustomizeProduct": "Personalizar produto",
  "facebookcapi.description.AddPaymentInfo":
    "A adição de informações de pagamento do cliente durante o processo de finalização da compra. Por exemplo, quando uma pessoa clica em um botão para salvar as informações de cobrança.",
  "facebookcapi.description.AddToCart":
    "A adição de um item ao carrinho ou cesto de compras. Por exemplo, clicar em um botão Adicionar ao carrinho em um site.",
  "facebookcapi.description.AddToWishlist":
    "A adição de itens à lista de desejos. Por exemplo, clicar em um botão Adicionar à lista de desejos em um site.",
  "facebookcapi.description.CompleteRegistration":
    "O envio de informações por parte de um cliente em troca do fornecimento de um serviço da empresa. Por exemplo, cadastro para assinatura de email.",
  "facebookcapi.description.Contact":
    "Um telefone, SMS, email, bate-papo ou outro tipo de contato entre um cliente e sua empresa.",
  "facebookcapi.description.CustomizeProduct":
    "A personalização de produtos por meio de uma ferramenta de configuração ou outro aplicativo que sua empresa possui.",
  "facebookcapi.description.Donate":
    "A doação de fundos para sua organização ou causa.",
  "facebookcapi.description.FindLocation":
    "Quando uma pessoa encontra uma de suas localizações na internet, com a intenção de visitar seu estabelecimento. Por exemplo, pesquisar um produto e encontrá-lo em uma de suas lojas locais.",
  "facebookcapi.description.InitiateCheckout":
    "O início do processo de finalização da compra. Por exemplo, clicar no botão Finalizar a compra.",
  "facebookcapi.description.Lead":
    "O envio de informações por parte do cliente, sabendo que poderá ser contatado posteriormente pela empresa. Por exemplo, enviar um formulário ou se cadastrar para uma avaliação.",
  "facebookcapi.description.PageView":
    "Este é o pixel padrão que rastreia visitas na página. Por exemplo, uma pessoa acessa as páginas de seu site",
  "facebookcapi.description.Purchase":
    "A conclusão de uma compra, geralmente indicada pelo recebimento de uma confirmação de pedido, compra ou recibo da transação. Por exemplo, ser direcionado para uma página de agradecimento ou confirmação.",
  "facebookcapi.description.Schedule":
    "A marcação de um horário para visitar uma de suas localizações.",
  "facebookcapi.description.Search":
    "Uma pesquisa realizada em seu site, aplicativo ou outra propriedade. Por exemplo, pesquisas de produtos ou viagens.",
  "facebookcapi.description.StartTrial":
    "O início da avaliação gratuita de um produto ou serviço que você oferece. Por exemplo, assinatura de avaliação.",
  "facebookcapi.description.SubmitApplication":
    "O envio de uma solicitação para um produto, serviço ou programa que você oferece. Por exemplo, cartão de crédito, programa educacional ou emprego.",
  "facebookcapi.description.Subscribe":
    "O início de uma assinatura paga referente a um produto ou serviço que você oferece.",
  "facebookcapi.description.ViewContent":
    "Uma visita a uma página da web importante para você. Por exemplo, uma página de destino ou página de produto. A opção Visualizar conteúdo informa se alguém visitou a URL de determinada página da web, sem informar o que ela fez ou viu na página.",
  "facebookcapi.Donate": "Doar",
  "facebookcapi.eventContext": "Contexto de Geração do Evento",
  "facebookcapi.eventName": "Evento",
  "facebookcapi.eventType": "Tipo de Evento",
  "facebookcapi.externalEvents": "Eventos em site externo",
  "facebookcapi.FindLocation": "Encontrar localização",
  "facebookcapi.InitiateCheckout": "Iniciar finalização da compra",
  "facebookcapi.Lead": "Cadastro",
  "facebookcapi.PageView": "Visualização de Página",
  "facebookcapi.pixel_id": "ID do Pixel",
  "facebookcapi.Purchase": "Compra",
  "facebookcapi.qiplusEvents": "Eventos do QIPlus",
  "facebookcapi.Schedule": "Programação",
  "facebookcapi.script": "Script para a página",
  "facebookcapi.Search": "Pesquisa",
  "facebookcapi.siteAction": "Ação no site",
  "facebookcapi.standardEvent": "Evento padrão",
  "facebookcapi.StartTrial": "Iniciar período de avaliação",
  "facebookcapi.SubmitApplication": "Enviar candidatura",
  "facebookcapi.Subscribe": "Assinar",
  "facebookcapi.ViewContent": "Visualizar conteúdo",
  "facebookleads.clickToUnconnect": "Clique para desconectar",
  "facebookleads.connectAsAdmin":
    "Faça login com o seu usuário administrador de páginas do Facebook",
  "facebookleads.connectedAs": "Conectado como ",
  "facebookleads.connectedWithLeadsAds": "Está conectado com o QIPlus",
  "facebookleads.connectedWithLeadsAdsInAnotherPost":
    "Está conectado com o QIPlus em outra integração",
  "facebookleads.connectLeadsAdsWith": "Conectar o QIPlus com",
  "facebookleads.connectPage": "Conectar com a página",
  "facebookleads.connectWith": "Conectar com",
  "facebookleads.connectYourPageToQIPlus":
    "Conecte sua página com o app QIPlus do Facebook",
  "facebookleads.facebookConnectPages": "Conectar a Páginas do Facebook",
  "facebookleads.loginbtn": "Login com Facebook",
  "facebookleads.loginbtnAs": "Faça login no Facebook como",
  "facebookleads.logoutbtn": "Sair do Facebook",
  "facebookleads.noPagesAvailable":
    "Não encontramos páginas associadas ao seu usuário. Certifique-se de que possui acesso de administrador nas mesmas e tente novamente",
  "facebookleads.page_access_token": "Token de acesso à sua página no Facebook",
  "facebookleads.page_id": "ID da sua página no Facebook",
  "facebookleads.qiplus_token": "Token do QIPlus",
  "facebookleads.qiplus_verify_token": "Token de verificação do QIPlus",
  "facebookleads.successfullyConnectedPage":
    "Parabéns! Você conectou sua página com o aplicativo QIPlus do Facebook",
  "facebookleads.successfullyUnconnectedPage":
    "Você desconectou sua página do aplicativo QIPlus do Facebook",
  "facebookleads.unconnectPageWarning":
    "Deseja desconectar sua página do aplicativo QIPlus do Facebook? Seus leads não serão mais enviados de sua página para a plataforma QIPlus",
  "facebookleads.userConflictAlert":
    "Esta integração foi realizada por outro usuário do Facebook",
  "facebookleads.userConflictPlaceholder":
    "Faça login como [%s] para poder editar a integração",
  "facebookleads.userLoginAlert":
    "Faça login com o usuário original para poder editar a integração",
  "fieldTypes.checkbox": "Checkbox (múltipla escolha)",
  "fieldTypes.date_picker": "Seletor de Data",
  "fieldTypes.email": "Email",
  "fieldTypes.number": "Número",
  "fieldTypes.password": "Senha",
  "fieldTypes.radio": "Botões de radio (resposta única)",
  "fieldTypes.select": "Seletor (resposta única)",
  "fieldTypes.text": "Texto Simples",
  "fieldTypes.textarea": "Área de Texto",
  "fieldTypes.url": "URL",
  "format.date": "DD/MM/YYYY",
  "format.date.full": "DD/MM/YYYY HH:mm:ss",
  "format.date.full.short.year": "DD/MM/YY HH:mm:ss",
  "format.date.full.short.year.short.hour": "DD/MM/YY HH:mm",
  "format.date.short.month": "MM/YYYY",
  "format.date.short.year": "YYYY",
  "forms.address": "Endereço",
  "forms.addresses": "Endereços",
  "forms.addressFields": "Campos de Endereço",
  "forms.avatar": "Avatar",
  "forms.basicFields": "Campos Básicos",
  "forms.birthday": "Data de Nascimento",
  "forms.button": "Botão",
  "forms.city": "Cidade",
  "forms.cnpj": "CNPJ",
  "forms.comp": "Complemento",
  "forms.companyName": "Nome da Empresa",
  "forms.complementary": "Complemento",
  "forms.contact": "Contato",
  "forms.contactId": "Contato",
  "forms.contract": "Contrato",
  "forms.contractNumber": "Número do Contrato",
  "forms.contracts": "Contratos",
  "forms.country": "País",
  "forms.countryCode": "Cód. País",
  "forms.cpf": "CPF",
  "forms.customField": "Campo Personalizado",
  "forms.customFields": "Campos Personalizados",
  "forms.dateAdded": "Adicionado",
  "forms.dateFields": "Datas",
  "forms.dateRegistered": "Data de Registro",
  "forms.description": "Descrição",
  "forms.displayName": "Nome de Contato",
  "forms.document": "Documento",
  "forms.document_number": "N de Documento",
  "forms.document_type": "Tipo de Documento",
  "forms.email": "Email",
  "forms.facebook": "Facebook",
  "forms.field": "Campo",
  "forms.fieldBlocks": "Blocos de Campos",
  "forms.fieldChoices": "Opções do Campo",
  "forms.fieldLabel": "Rótulo do Campo",
  "forms.fieldName": "Nome do Campo",
  "forms.fieldPlaceholder": "Texto de Fundo",
  "forms.fields": "Campos",
  "forms.fieldType": "Tipo de Campo",
  "forms.fieldValue": "Valor do Campo",
  "forms.firstName": "Nome",
  "forms.formURL": "Url do Formulário",
  "forms.fullName": "Nome Completo",
  "forms.gender": "Gênero",
  "forms.gender.fem": "Feminino",
  "forms.gender.masc": "Masculino",
  "forms.genderSelect": "Selecione o Gênero",
  "forms.id": "ID",
  "forms.ID": "ID",
  "forms.instagram": "Instagram",
  "forms.lastName": "Sobrenome",
  "forms.linkedin": "LinkedIn",
  "forms.manager": "Gerente de Contas",
  "forms.manager:email": "Email do Gerente de Contas",
  "forms.manager:mobile": "Celular do Gerente de Contas",
  "forms.manager:name": "Nome do Gerente  do de Contas",
  "forms.manager:phone": "Telefone do Gerente de Contas",
  "forms.managerEmail": "Email do Gerente de Contas",
  "forms.managerMobile": "Celular do Gerente de Contas",
  "forms.managerName": "Nome do Gerente  do de Contas",
  "forms.managerPhone": "Telefone do Gerente de Contas",
  "forms.maritalStatus": "Estado Civil",
  "forms.maritalStatus.divorced": "Divorciado/a",
  "forms.maritalStatus.married": "Casado/a",
  "forms.maritalStatus.other": "Outro",
  "forms.maritalStatus.single": "Solteiro/a",
  "forms.maritalStatus.widowed": "Viúvo/a",
  "forms.mobile": "Celular",
  "forms.mobileWithCC": "Celular com Código de País e DDD",
  "forms.modified": "Modificado",
  "forms.name": "Nome",
  "forms.names": "Nomes",
  "forms.neighborhood": "Bairro",
  "forms.newField": "Novo Campo",
  "forms.nickname": "Nome de Usuário",
  "forms.num": "Número",
  "forms.occupation": "Profissão",
  "forms.optionAdd": "Adicionar Opção",
  "forms.optionLabel": "Rótulo da Opção",
  "forms.optionValue": "Valor da Opção",
  "forms.other": "Outros",
  "forms.password": "Senha",
  "forms.paymentDetails": "Detalhes do Pagamento",
  "forms.paymentMethod": "Forma de Pagamento",
  "forms.personalize": "Personalizar",
  "forms.phone": "Telefone",
  "forms.postalCode": "CEP",
  "forms.professional": "Profissional",
  "forms.professionals": "Profissionais",
  "forms.profile": "Perfil",
  "forms.promoter": "Promotor",
  "forms.promoter:email": "Email do Promotor",
  "forms.promoter:mobile": "Celular do Promotor",
  "forms.promoter:name": "Nome do Promotor",
  "forms.promoter:phone": "Telefone do Promotor",
  "forms.promoterEmail": "Email do Promotor",
  "forms.promoterMobile": "Celular do Promotor",
  "forms.promoterName": "Nome do Promotor",
  "forms.promoterPhone": "Telefone do Promotor",
  "forms.pts": "Pontuação",
  "forms.pts_gained": "Pontuação Gerado",
  "forms.qiFields": "Campos do QI Plus",
  "forms.qrcode": "Código QR",
  "forms.qualification": "Qualificação",
  "forms.readonly": "Somente Leitura",
  "forms.redirect":
    "Redirecionar Leads a outro destino após o envio do formulário?",
  "forms.ref": "Referência",
  "forms.required": "Obrigatório",
  "forms.requiredFields": "Campos obrigatórios",
  "forms.rg": "RG",
  "forms.saveCustomField": "Salvar no banco de dados",
  "forms.score": "Score",
  "forms.score_gained": "Score Gerado",
  "forms.select.birthdayMonth": "Mês de aniversário",
  "forms.seller": "Vendedor",
  "forms.seller:email": "Email do Vendedor",
  "forms.seller:mobile": "Celular do Vendedor",
  "forms.seller:name": "Nome do Vendedor",
  "forms.seller:phone": "Telefone do Vendedor",
  "forms.sellerEmail": "Email do Vendedor",
  "forms.sellerMobile": "Celular do Vendedor",
  "forms.sellerName": "Nome do Vendedor",
  "forms.sellerPhone": "Telefone do Vendedor",
  "forms.social": "Redes Sociais",
  "forms.socialFields": "Campos de Redes Sociais",
  "forms.socialNewtork": "Rede Social",
  "forms.socialNewtorks": "Redes Sociais",
  "forms.stage": "Etapa",
  "forms.state": "Estado",
  "forms.street": "Rua",
  "forms.street_number": "Número",
  "forms.submitConfirmPage": "Página de confirmação",
  "forms.tags": "Tags",
  "forms.team": "Equipe",
  "forms.thankYouMessage": "Mensagem de confirmação",
  "forms.ticket": "Pedido",
  "forms.ticketItems": "Itens do pedido",
  "forms.ticketNumber": "Número do pedido",
  "forms.tickets": "Pedidos",
  "forms.title": "Título",
  "forms.twitter": "Twitter",
  "forms.type": "Tipo de Contato",
  "forms.type.corporation": "Empresa",
  "forms.type.individual": "Pessoa Física",
  "forms.updatedAt": "Modificado em",
  "forms.url": "Site",
  "forms.zipcode": "CEP",
  "funnels.cartMode": "Cesta de Compras",
  "funnels.contactedStage": "Contatado",
  "funnels.negotiatingStage": "Em Negociação",
  "funnels.proposedStage": "Proposta Realizada",
  "funnels.prospectStage": "Prospecção",
  "funnels.singleProductMode": "Produto Único",
  "funnels.singleValueMode": "Valor Fechado",
  "funnels.tooltip.actions":
    "Monte aqui o seu plano, quais serão os passos da automação desejada a partir de cada etapa do seu cliente.",
  "funnels.tooltip.create": "Inicie a jornada do seu cliente.",
  "funnels.tooltip.goal":
    "As metas predefinidas serão mostradas na pipeline para os vendedores/gerentes.",
  "funnels.tooltip.pipeline":
    " Defina as etapas de vendas e tarefas do seu time. \n Perdidos: Negócios perdidos. \n Ganhos: Negócios ganhos.",
  "funnels.tooltip.sales_mode":
    "A modalidade de vendas afetará o valor do negócio, assim como a forma que o vendedor interage com o card de negócios. \n Cesta de Compras: O vendedor seleciona quais produtos o cliente comprou, e gera um ticket de venda precificado de acordo com o valor dos produtos selecionados. \n Valor Negociado: O vendedor insere o valor acordado com o cliente. \n Valor Fechado: Todos os negócios são fechados com o valor predefinido.",
  "funnels.tooltip.shotx": "Escolha as instâncias disponíveis",
  "funnels.tooltip.store":
    "Campo opcional. É recomendado adicionar lojas afim de segmentar a origem das compras. Ex: Empresas que possuem mais de uma unidade.",
  "funnels.tooltip.title":
    "Escreva o título do seu funil de vendas. \n Ex: Captação de Leads da Landing Page.",
  "funnels.valuesMode": "Valor Negociado",
  "goals.checkin": "Check-in",
  "goals.confirmed": "Participantes Confirmados",
  "goals.daily": "Diária",
  "goals.final": "Final",
  "goals.monthly": "Mensal",
  "goals.participants": "Participantes",
  "goals.registers": "Registros",
  "goals.views": "Visualizações",
  "goals.weekly": "Semanal",
  "googleads.loginAsAdmin": "Buscar no Google Ads",
  "hint.searchMailList": "Pesquisar nos emails",
  "hint.whatAreYouLookingFor": "What are You Looking For",
  "icon.stock": "ti-server",
  "import.updateData": "Atualizar Registros Existentes",
  "instructions.acountInvestments":
    "Insira os investimentos realizados (título, valor, periodicidade) para que possamos calcular as métricas de retorno do investimento",
  "instructions.investmentQuickField":
    "Você pode usar este campo para simular as métricas de retorno do investimento, mesmo que tenha informado em suas configurações os investimentos realizados",
  "instructions.investments":
    "Insira os investimentos realizados (título e valor) para obter as métricas de retorno do investimento",
  "instructions.leaveBlankForModel":
    "Deixe em branco se utilizar apenas como modelo",
  "integrations.addTags": "Adicionar Tags",
  "integrations.apiKey": "API Key",
  "integrations.apiUrl": "URL da API",
  "integrations.appID": "App ID",
  "integrations.associateCustomFields": "Associar Campos Personalizados",
  "integrations.bluesoft": "BlueSoft",
  "integrations.customSMTP": "SMTP Próprio",
  "integrations.default": "Default",
  "integrations.destinationUrl": "Url de Destino",
  "integrations.eduzz": "Eduzz",
  "integrations.elementor": "Elementor",
  "integrations.emailField": "Campo Email",
  "integrations.encriptation": "Tipo de Encriptação",
  "integrations.facebookads.selectCampaign": "Selecione uma campanha",
  "integrations.fieldAssociated": "Campo associado",
  "integrations.fieldIDOnSource": "ID ou nome do campo na plataforma",
  "integrations.fieldToAssociate": "Campo a associar",
  "integrations.form": "Forms",
  "integrations.formID": "Form ID",
  "integrations.g_calendar": "Google Calendar",
  "integrations.gcalId": "Id do calendário",
  "integrations.gformsFunction": "Função para o script do forms",
  "integrations.gmail": "Gmail SMTP",
  "integrations.googleads.connect": "Conectar Google Ads",
  "integrations.googleads.customerDescription": "Descricão do cliente",
  "integrations.googleads.customerId": "ID do cliente",
  "integrations.googleads.customers": "Google Ads - Clientes",
  "integrations.googleads.customersInstructions":
    "Insira aqui os clientes do Google Ads que você administra.",
  "integrations.googleads.getReport": "Obter relatório",
  "integrations.googleforms": "Google Forms",
  "integrations.googleformsCollectEmail":
    "O form está configurado para coletar e-mails",
  "integrations.hotmart": "Hotmart",
  "integrations.imap": "Integração IMAP",
  "integrations.imapSettings": "Configurações de Entrada IMAP",
  "integrations.importTags": "Importar Tags",
  "integrations.importTags.instructions":
    "Importar as etiquetas do lead na plataforma de origem, quando disponíveis",
  "integrations.infusionsoft": "Infusion Soft",
  "integrations.leadlovers": "Lead Lovers",
  "integrations.mailDomainActive": "Seu Dominio esta Ativo",
  "integrations.mailDomainAlreadyExists":
    "O dominio [%domain] já está configurado em uma integração.",
  "integrations.mailDomainCheck": "Verificar Dominio",
  "integrations.mailDomainChecking": "Verificando Dominio",
  "integrations.mailDomainInactive":
    "Seu Dominio não está configurado, por favor verifique",
  "integrations.mailDomainInstruction":
    "Acesse o provedor DNS que você usa para gerenciar <b>[%domain]</b> e adicione os seguintes registros DNS.",
  "integrations.mailDomainMensagem":
    "Recomendamos usar o subdomínio mail para fazer a configuração. Ex: mail.meudominio.com",
  "integrations.mailDomainName": "Dominio do Remetente",
  "integrations.mailDomainPersonalized": "Email Personalizado",
  "integrations.mandeumzap": "Mande um Zap",
  "integrations.mautic": "Mautic",
  "integrations.nameGeneric": "Nome do WebHook",
  "integrations.notazz": "Notazz",
  "integrations.optionGeneric": "Agrupamento do WebHook",
  "integrations.phoneNumber": "Número de Telefone",
  "integrations.priorityRecords": "Prioridade de Recebmento do DNS",
  "integrations.rdstation": "RD Station",
  "integrations.recordSendValue": "Tipo de Gravacao do Envio",
  "integrations.recordsValueDNS": "Valor de Gravação",
  "integrations.recordType": "Tipo de Gravação",
  "integrations.reports.configure": "Configurar",
  "integrations.reports.noIntegrations": "Nenhuma integração configurada",
  "integrations.reports.selectIntegrationAndAccount":
    "Seleciona uma integração e uma conta",
  "integrations.reports.selectIntegrationAndCustomer":
    "Selecione uma integração e um cliente",
  "integrations.selectOneIntegration": "Selecione uma integração",
  "integrations.senderEmail": "Email do Remetente",
  "integrations.senderName": "Nome do Remetente",
  "integrations.senderPass": "Senha do Remetente",
  "integrations.sendNameDNS": "Nome de envio",
  "integrations.sendValue": "Valor de Envio",
  "integrations.shopify": "Shopify",
  "integrations.smtp": "Integração SMTP",
  "integrations.smtpHost": "Host SMTP",
  "integrations.smtpPort": "Porta SMTP",
  "integrations.smtpSettings": "Configurações de Saída SMTP",
  "integrations.tags": "Tags",
  "integrations.tags.instructions":
    "Indique as Tags que deseja atribuir aos contatos adicionados através desta integração.",
  "integrations.webhookURL": "Webhook URL",
  "integrations.woocommerce": "WooCommerce",
  "integrations.yourMailDomain": "seu dominio",
  "interactions.module.singular": "interação",
  "interactions.module.title": "Interações",
  "item.add": "Novo item",
  "item.clone": "Clonar item",
  "item.create": "Criar item",
  "item.edit": "Editar item",
  "items.clone": "Clonar itens",
  "items.edit": "Editar itens",
  "items.price": "Valor",
  "items.product": "Produto",
  "items.qty": "Unidades",
  "leads.addedToSegmentation": "Leads adicionados à segmentação",
  "leads.basicInfo": "Dados Básicos",
  "leads.corporation": "Empresa",
  "leads.deleteConfirmationBody": "O lead será eliminado permanentemente",
  "leads.deleteConfirmationTitle": "Tem certeza que deseja eliminar este lead?",
  "leads.dupLead": "Já existe um lead com estes dados",
  "leads.individual": "Pessoa Física",
  "leads.switchLead": "Deseja editar o lead?",
  "leads.type": "Tipo de Contato",
  "logs.noLogsYet": "Não há logs por enquanto",
  "logs.viewAllLogFields": "Ver todos",
  "logs.viewGroupedLogFields": "Ver resumo",
  "mail.errors.emailTestFailed": "Ocorreu um erro ao enviar o email de teste",
  "mail.errors.emptyFromName": "Preencha o nome do remetente",
  "mail.errors.emptySendMethod": "Escolha o método de envio",
  "mail.errors.emptySubject": "Preencha o campo assunto",
  "mail.errors.errorSendingEmail": "Ocorreu um erro ao enviar o email",
  "mail.errors.errorSendingEmails": "Ocorreu um erro ao enviar o(s) email(s)",
  "mail.errors.invalidBody": "O conteúdo do email não pode ser nulo",
  "mail.errors.invalidFromName": "O nome do remetente não pode ser nulo",
  "mail.errors.invalidRecipients": "Destinatário inválido",
  "mailboxes.authenticateInGmail": "Autentique sua conta do Gmail",
  "mailboxes.availableMailboxes":
    "Você pode user algum destes endereços disponíveis:",
  "mailboxes.confirmCloudMailboxCreation":
    "Será criada uma nova caixa de entrada e o email para envio não poderá ser alterado posteriormente para esta Inbox",
  "mailboxes.confirmCreation": "Confirme a criação da sua Caixa de Entrada",
  "mailboxes.draft": "Rascunhos",
  "mailboxes.IMAP": "IMAP - Domínio Próprio",
  "mailboxes.inbox": "Inbox",
  "mailboxes.labels.CATEGORY_FORUMS": "Forums",
  "mailboxes.labels.CATEGORY_PERSONAL": "Personal",
  "mailboxes.labels.CATEGORY_PROMOTIONS": "Promotions",
  "mailboxes.labels.CATEGORY_SOCIAL": "Social",
  "mailboxes.labels.CATEGORY_UPDATES": "Updates",
  "mailboxes.labels.CHAT": "Chat",
  "mailboxes.labels.DRAFT": "Rascunho",
  "mailboxes.labels.IMPORTANT": "Importante",
  "mailboxes.labels.INBOX": "Inbox",
  "mailboxes.labels.READ": "Lida",
  "mailboxes.labels.SENT": "Sent",
  "mailboxes.labels.SPAM": "Spam",
  "mailboxes.labels.STARRED": "Com estrela",
  "mailboxes.labels.trash": "Lixeira",
  "mailboxes.labels.TRASH": "Lixeira",
  "mailboxes.labels.UNREAD": "Não lida",
  "mailboxes.labels.UNSTARRED": "Sem estrela",
  "mailboxes.sent": "Enviados",
  "mailboxes.spam": "Spam",
  "mailboxes.trash": "Lixeira",
  "mailboxes.unavailableEmailAddress":
    "O endereço de email não está disponível",
  "managers.roulette": "Roleta de Gerentes",
  "menu.account": "Minha Conta",
  "menu.app": "App",
  "menu.campaigns": "Campanhas",
  "menu.contact": "Contato",
  "menu.contacts": "Contatos",
  "menu.dashboard": "Dashboard",
  "menu.deals": "Negócios",
  "menu.hunters": "QI Hunter",
  "menu.import": "Importar",
  "menu.managers": "Gerentes",
  "menu.model": "Modelo",
  "menu.models": "Modelos",
  "menu.myProfile": "Meu Perfil",
  "menu.reports": "Relatórios",
  "menu.segmentationsGroup": "Segmentações",
  "menu.sellers": "Vendedores",
  "menu.tags": "Tags",
  "menu.teams": "Equipes",
  "menu.tools": "Ferramentas",
  "menu.tooltip.dashboard": "Veja aqui o desenvolvimento do seu negócio",
  "menu.users": "Usuários",
  "message.shotx.none": "Não há ShotX configurado",
  "messagesBroadcast.changed": "Mensagem em massa atualizada com sucesso",
  "messagesBroadcast.error": "Algo aconteceu ao atualizar mensagem em massa",
  "messagesBroadcast.module.description": "Enviando mensagens em massa",
  "messagesBroadcast.module.title": "Mensagens em Massa",
  "messagesBroadcast.schedule.error":
    "Algo aconteceu ao agendar envio de mensagem em massa",
  "messagesBroadcast.schedule.success":
    "Envio de mensagem em massa agendado com sucesso",
  "messagesBroadcast.save.success":
    "Broadcast salvo com sucesso",
  "messagesBroadcast.send.error": "Algo aconteceu ao enviar mensagem em massa",
  "modal.banner.buttonPrimary": "Adquirir ShotX",
  "modal.banner.buttonSecondary": "Cancelar",
  "modal.banner.checkedNoShowAgain": "Não Exibir Novamente",
  "modal.banner.title": "Conhecendo o ShotX",
  "modal.cancel": "Cancelar",
  "modal.close": "Fechar",
  "modal.export": "Exportar",
  "modal.export.completed": "Exportar todas colunas",
  "modal.export.csv": "Exportar para CSV",
  "modal.export.resumed": "Exportar Principais Colunas",
  "modal.export.xlsx": "Exportar para XLSX",
  "modal.title": "Exportar registros",
  "module.api": "Api",
  "module.backend": "Backend",
  "module.drafts": "Rascunhos",
  "module.frontend": "Frontend",
  "module.inbox": "Inbox",
  "module.issue": "Issue",
  "module.sent": "Enviados",
  "module.spam": "Spam",
  "module.trash": "Lixeira",
  "modules.app.name": "qiplus",
  "modules.quick_messages": "Mensagens Rápidas",
  "modules.quick_messages.added": "Mensagem Rápida Criada",
  "modules.quick_messages.changed": "Mensagem Rápida Atualizada",
  "modules.quick_messages.removed": "Mensagem Rápida Removida",
  "modules.quick_messages.short": "Mensagem Rápida",
  "modules.quick_messages.singular": "Mensagem Rápida",
  "nf.aliquotas": "Alíquotas",
  "nf.apiError": "Ocorreu um erro ao gerar a Nota Fiscal",
  "nf.cnae": "CNAE",
  "nf.competence": "Período Faturado",
  "nf.customer": "Cliente",
  "nf.goal": "Finalidade",
  "nf.ie": "Inscrição Estadual",
  "nf.im": "Inscrição Municipal",
  "nf.iss_check": "ISS retido na fonte",
  "nf.issue_date": "Data de Emissão",
  "nf.LC116": "Item da Lista de Serviço da LC 116",
  "nf.loadingMessage": "Gerando Nota Fiscal",
  "nf.nature_operation": "",
  "nf.operation_type": "Tipo de Operação",
  "nf.product": "Produto",
  "nf.referenced": "Nota fiscal referenciada",
  "nf.service": "Serviço",
  "nf.service_code": "Código de serviço do município",
  "nf.service_description": "Descrição do serviço do município",
  "nf.successMessage": "Nota Fiscal gerada com successo!",
  "nf.taxtype": "Tipo de Pessoa",
  "nf.title": "Nota Fiscal",
  "nf.type": "Tipo de Nota Fiscal",
  "nf.type_product": "Produtos",
  "nf.type_service": "Serviços",
  "notification.check": "Notificação",
  "notification.interval": "",
  "notification.qty": "",
  "notifications.check": "Notificações",
  "notifications.emptyMsg": "Nenhum alerta por enquanto",
  "pagarme.openDashboard": "Ver no Dashboard Pagarme",
  "pagarme.openManageUrl": "Editar com Pagarme",
  "pagarme.openPendingUpdate": "Ver update no Pagarme",
  "participants.checkin": "Check-in confirmado",
  "participants.checkinTime": "Check-in em",
  "participants.confirm": "Confirmar Participante",
  "participants.confirmed": "Participante confirmado",
  "participants.confirmedTime": "Confirmado em",
  "participants.disconfirm": "Desconfirmar Participante",
  "participants.disconfirmed": "Participante desconfirmado",
  "participants.undoCheckin": "Check-in cancelado",
  "payment.accountDoesNotExist": "A conta não existe ou não se encontra ativa",
  "payment.accountHasPendingUpdates": "Esta conta possui upgrades pendentes",
  "payment.accountIsNotActive":
    "Ops, parece que sua conta não se encontra ativa",
  "payment.accountType": "Tipo de conta",
  "payment.activeSubscriptionAlert":
    "Já existe uma assinatura ativa para esta conta. Cancele a assinatura antes de gerar uma nova",
  "payment.agencia": "Agência",
  "payment.agencia_dv": "DV da Agência",
  "payment.bankAccount": "Conta Bancária",
  "payment.bankCode": "Código do Banco",
  "payment.billing": "Cobrança",
  "payment.billingAccountNotFound":
    "Você ainda não possui uma conta ativa no QI Plus. Crie agora sua conta ou comunique-se conosco para conhecer mais sobre o QIPlus.",
  "payment.boleto": "Boleto Bancário",
  "payment.cancelPendingUpdates": "Cancelar Assinaturas Pendentes",
  "payment.cancelSubscription": "Cancelar Assinatura",
  "payment.cancelSubscriptionError": "Ocorreu um erro ao cancelar a assinatura",
  "payment.cardCustomerName": "NOME NO CARTÃO",
  "payment.cash": "Pagamento em Espécie",
  "payment.checkout": "Checkout",
  "payment.checkoutSuccess": "Checkout realizado com successo",
  "payment.commission": "Comissão",
  "payment.commissions": "Comissões",
  "payment.confirmCancelSubscription":
    "Tem certeza de que deseja cancelar a assinatura? Essa ação não poderá ser revertida posteriormente",
  "payment.confirmCheckout": "Fazer checkout mesmo assim",
  "payment.confirmPayment": "Confirmar Pagamento",
  "payment.conta": "Número da conta",
  "payment.conta_dv": "DV da Conta",
  "payment.credit_card": "Cartão de Crédito",
  "payment.delivery": "Delivery",
  "payment.delivery_fee": "Taxa de Delivery",
  "payment.discount": "Desconto",
  "payment.discounts": "Descontos",
  "payment.document_number": "Documento (CPF ou CNPJ)",
  "payment.extras": "Avulsos",
  "payment.gateway": "Forma de Pagamento",
  "payment.gateway_type": "Meio de Cobrança",
  "payment.generateBoleto": "Gerar Boleto para Pagamento",
  "payment.implementation": "Implementação",
  "payment.implementationAlreadyCharged":
    "Implementação já cobrada na primeira assinatura",
  "payment.implementationCost": "Custo de implementação",
  "payment.installment": "Parcela",
  "payment.installments": "Parcelas",
  "payment.legal_name": "Titular",
  "payment.maxInstallments": "Em até",
  "payment.mercadoPagoCredentials": "Credenciais Mercado Pago",
  "payment.noMonthlySubscription": "O plano não dispõe de assinatura mensal",
  "payment.oneTimePayment": "à vista",
  "payment.other_discounts": "Outros descontos",
  "payment.other_fees": "Outras taxas",
  "payment.other_taxes": "Outros impostos",
  "payment.paymentLink": "Link de pagamento",
  "payment.planDetails": "Detalhes do Plano",
  "payment.planValue": "Valor do Plano",
  "payment.pleaseCheckYourEmail":
    "Por favor, verifique seu email para acessar o link de pagamento ou alterar a forma de pagamento",
  "payment.price": "Preço",
  "payment.prices": "Preços",
  "payment.printYourBoleto": "Imprima seu boleto para realizar o pagamento",
  "payment.pts": "Pontos QI-Fidelidade",
  "payment.recipient": "Recebedor",
  "payment.recurrency": "Recorrência",
  "payment.secureCheckout": "Compra 100% segura",
  "payment.startingAt": "A partir de",
  "payment.subscription": "Assinatura",
  "payment.taxes": "Impostos",
  "payment.ticketValue": "Total do Pedido",
  "payment.total": "Total",
  "payment.totals": "Totais",
  "payment.updatesWillBeReadyOnPaymentConfirm":
    "A atualização da conta será ativada automaticamente após a confirmação do pagamento",
  "payment.upgrade": "Upgrade",
  "payment.validThru": "valido até",
  "payment.value": "Valor",
  "payment.values": "Valores",
  "payment.viaCreditCard": "no cartão",
  "payment.yourAccountWillBeReadyOnPaymentConfirm":
    "Sua conta estará ativa automaticamente após a confirmação do pagamento",
  "payment.yourAccountWillBeReadySoon":
    "Sua conta estará ativa dentro de alguns momentos",
  "pipeline.addOneStage": "Adicionar uma Etapa",
  "pipeline.addStage": "Adicionar Etapa",
  "pipeline.createSegmentation": "Criar Segmentação",
  "pipeline.dstStage": "Etapa de Destino",
  "pipeline.options": "Opções da Pipeline",
  "pipeline.orderby": "Ordenar por",
  "pipeline.orderByMensages": "Quantidade de mensagens não lidas",
  "pipeline.orderByModified": "Data de Modificação",
  "pipeline.orderByScoring": "Scoring",
  "pipeline.removeOneStage": "Remover uma Etapa",
  "pipeline.removeStage": "Remover Etapa",
  "pipeline.stage": "Etapa",
  "pipeline.stages": "Etapas",
  "placeholders.confirmOwnerUpdate": "Deseja atualizar conta a [%s]?",
  "placeholders.confirmRemoval": "Deseja eliminar [%s]?",
  "placeholders.confirmUpdate": "Deseja atualizar [%s]?",
  "placeholders.create": "Criar [%s]",
  "placeholders.customMailNotValid":
    "O remetente não pertence ao domínio personalizado",
  "placeholders.email": "Email",
  "placeholders.errorFetchingPosts":
    "Ocorreu um erro ao carregar [%s]. Tente novamente",
  "placeholders.errorTrashingPosts":
    "Ocorreu um erro ao eliminar [%s]. Tente novamente",
  "placeholders.errorUpdatingPosts":
    "Ocorreu um erro ao atualizar [%s]. Tente novamente",
  "placeholders.events.checkin": "Checkin de Participantes de [%s]",
  "placeholders.events.participants": "Participantes de [%s]",
  "placeholders.inexistsErrorMsg": "[%s] não existe no banco de dados",
  "placeholders.invalidField": "O campo [%s] é inválido",
  "placeholders.logoutAlert":
    "É necessário fazer logout da sua conta atual [%s] para conectar uma nova conta",
  "placeholders.maxEmailsError":
    "A quantidade máxima de emails para o campo [%s] é [%max]",
  "placeholders.maxValueError": "O valor máximo para o campo [%s] é [%max]",
  "placeholders.minEmailsError":
    "A quantidade mínima de emails para o campo [%s] é [%min]",
  "placeholders.minRowsError": "Selecione ao menos [%min] [%s]",
  "placeholders.minValueError": "O valor mínimo para o campo [%s] é [%min]",
  "placeholders.MODULE_LIMIT_AVAILABLE":
    "O limite remanescente de [%s] em sua conta é [%n]",
  "placeholders.MODULE_LIMIT_REACHED":
    "Você atingiu o limite de [%s] da sua conta. Faça um upgrade para aumentar o limite",
  "placeholders.noGoalsFound":
    "Você ainda não carregou as informações de metas em [%s].",
  "placeholders.noInvestmentsFound":
    "Você ainda não carregou as informações de investimentos em [%s].",
  "placeholders.noPostsFound": "Não há [%s] em sua conta",
  "placeholders.noResultsFound": "Não há [%s] que coincidam com a sua busca",
  "placeholders.noUsersFound": "Não há [%s] em sua conta",
  "placeholders.password": "Senha",
  "placeholders.requiredField": "O campo [%s] é obrigatório",
  "placeholders.select": "Selecione...",
  "placeholders.selectAnItem": "Selecione [%s]",
  "placeholders.SMTP_SETTINGS_CONFIRM_EMAAIL":
    '<p><img alt="" src="https://qiplus.com.br/ckfiles/i9TtRqSINnaVr0f7x4uDBtk6SmY2/images/template-logo-600x200.jpg" style="height:200px; width:600px" /></p><p>&nbsp;</p><p>Parabéns, [%s]!</p><p>&nbsp;</p><p>Suas configura&ccedil;&otilde;es de SMTP foram corretamente configuradas!</p><p>&nbsp;</p><p><strong>Equipe QIPlus</strong></p>',
  "placeholders.thankYouMessageLabel": "Obrigado!",
  "placeholders.updateCampaignSettings":
    "Complete as configurações da campanha para ter acesso aos relatórios completos do QIPlus.",
  "placeholders.updateSettings":
    "Complete as configurações de [%s] para ter acesso aos relatórios completos do QIPlus.",
  "plansQiplus": "Planos Qiplus",
  "policy.cookiesAcceptanceEntries":
    "Ver registros da aceitação da Política de cookies",
  "policy.cookiesPolicy": "Política de Cookies",
  "preferences.currency": "BRL",
  "preferences.currencySymbol": "R$",
  "preferences.locale": "pt-BR",
  "privacy.privacyAcceptanceEntries":
    "Ver registros da aceitação da Política de privacidade",
  "privacy.privacyPolicy": "Política de privacidade",
  "privacy.privacyScript": "Script para aceitação da Política de privacidade",
  "privacy.privacyText": "Texto da Política de privacidade",
  "privacy.privacyType": "Conteúdo da Política de privacidade",
  "privacy.privacyUrl": "URL da política de privacidade",
  "privacy.termsAcceptanceEntries":
    "Ver registros da aceitação dos Termos de uso",
  "privacy.termsOfUse": "Termos de uso",
  "privacy.termsScript": "Script para aceitação dos Termos de uso",
  "privacy.termsText": "Texto dos Termos de uso",
  "privacy.termsType": "Conteúdo dos Termos de uso",
  "privacy.termsUrl": "URL dos termos de uso",
  "qiusers.corporation": "Empresa",
  "qiusers.deleteConfirmationBody": "O usuário será eliminado permanentemente",
  "qiusers.deleteConfirmationTitle":
    "Tem certeza que deseja eliminar este usuário?",
  "qiusers.dupLead": "Já existe um usuário com estes dados",
  "qiusers.individual": "Pessoa Física",
  "qiusers.switchLead": "Deseja editar o usuário?",
  "qiusers.type": "Tipo de Usuário",
  "questionnaires.answer": "Resposta",
  "questionnaires.answers": "Respostas",
  "questionnaires.question": "Pergunta",
  "questionnaires.questions": "Perguntas",
  "questionnaires.score": "Pontuação",
  "questionnaires.scores": "Pontuações",
  "quickMessages.add": "Nova Mensagem",
  "quickMessages.cancel": "Cancelar",
  "quickMessages.create": "Criar Mensagem",
  "quickMessages.edit": "Editar Mensagem",
  "quickMessages.enabled": "Habilitado",
  "quickMessages.enabledOnAutomation": "Automação",
  "quickMessages.enabledOnQuickMessage": "Mensagens rápidas",
  "quickMessages.message": "Mensagem",
  "quickMessages.module.singular": "Mensagem Rápida",
  "quickMessages.module.title": "Mensagens Rápidas",
  "quickMessages.module.tooltip": "Mensagens Rápidas para Conversas",
  "quickMessages.no": "Não",
  "quickMessages.releaseOn": "Disponível em",
  "quickMessages.tooltipAutomation":
    "Estará disponível para seleção nas automações do ShotX",
  "quickMessages.tooltipShotx":
    "Estará disponível para enviar mensagens com um clique para um negócio pelo ShotX",
  "quickMessages.vars": "Variáveis",
  "quickMessages.vars.clientName": "Nome do Cliente",
  "quickMessages.vars.vendorName": "Nome do Vendedor",
  "quickMessages.yes": "Sim",
  "redirect.customUrl": "Url personalizada",
  "redirect.qiplusPage": "Página do QIPlus",
  "repeater.removeOneItem": "Remover um Item",
  "reports.accepted": "Aceitos",
  "reports.accepted.singular": "Aceito",
  "reports.ads": "de Anúncios",
  "reports.ageRangeError":
    "O intervalo de idade é inválido, o inicio deve ser menor que o fim",
  "reports.clicked": "Clicados",
  "reports.clicked.singular": "Clicado",
  "reports.clickLink": "Clique no Link",
  "reports.clicks": "Cliques",
  "reports.complained": "Reclamações",
  "reports.complained.singular": "Reclamação",
  "reports.conversionsValue": "Conversões",
  "reports.cost": "Custo",
  "reports.costMicros": "Custos",
  "reports.costs": "Custos",
  "reports.cpc": "Custo por Clique",
  "reports.ctr": "CTR",
  "reports.delivered": "Entregues",
  "reports.delivered.singular": "Entregue",
  "reports.engagement": "Engajamento",
  "reports.error.singular": "Não entregue",
  "reports.errorMsg": "Selecione uma página ou redefina o intervalo de datas",
  "reports.failed": "Não entregues",
  "reports.failed.singular": "Não entregue",
  "reports.followers": "Seguidores",
  "reports.frequency": "Frequência",
  "reports.hardBounces": "Hard Bounces",
  "reports.impressions": "Impressões",
  "reports.interactions": "Interações com Conteúdo",
  "reports.interactions": "Interações",
  "reports.interactionsTax": "Taxa de interações",
  "reports.last_28d": "Ultimos 28 dias",
  "reports.last_3d": "Ultimos 3 dias",
  "reports.last_7d": "Ultimos 7 dias",
  "reports.last_90d": "Ultimos 90 dias",
  "reports.last_month": "Ultimo Mês",
  "reports.last_week_sun_sat": "Ultima Semana",
  "reports.last_year": "Ultimo Ano",
  "reports.metrics": "Métricas",
  "reports.noData": "Nenhum dado para mostrar",
  "reports.opened": "Abertos",
  "reports.opened.singular": "Aberto",
  "reports.organic": "de Organico",
  "reports.page_fan_adds_unique": "Seguidores",
  "reports.page_fan_adds_unique_Description":
    "O número de novas pessoas que curtiram a sua Página.",
  "reports.page_impressions": "Impressões de Página",
  "reports.page_impressions_Description":
    "O número de vezes que qualquer conteúdo da sua Página ou sobre ela apareceu na tela de uma pessoa. Isso inclui publicações, stories, anúncios, além de outros conteúdos ou informações da sua Página.",
  "reports.page_posts_impressions": "Publicações da Página",
  "reports.page_posts_impressions_Description":
    "O número de vezes que as publicações da sua Página apareceram na tela de uma pessoa. As publicações incluem status, fotos, links, vídeos e muito mais.",
  "reports.page_views_total": "Exibições de página",
  "reports.page_views_total_Description":
    "O número de vezes que o perfil de uma Página foi visualizado por pessoas conectadas e desconectadas.",
  "reports.preset_date": "Selecionar Periodo",
  "reports.range": "Alcance",
  "reports.rangeDescription":
    "Essa métrica conta o alcance da distribuição orgânica ou paga do seu conteúdo no Facebook, incluindo publicações, stories e anúncios. Ela também inclui o alcance de outras fontes, como marcações, check-ins e visitas à Página ou ao perfil. Esse número também inclui o alcance de publicações e stories que foram turbinados. O alcance só é calculado uma vez se ocorrer por meio da distribuição orgânica e paga. Essa métrica é estimada.",
  "reports.reach": "Alcance",
  "reports.recipient": "Destinatário",
  "reports.recipients": "Destinatários",
  "reports.rejected": "Rejeitados",
  "reports.rejected.singular": "Rejeitado",
  "reports.replied.singular": "Respondido",
  "reports.selectPage": "Selecionar Pagina",
  "reports.sent": "Enviados",
  "reports.sent.singular": "Enviado",
  "reports.shots": "Disparos",
  "reports.softBounces": "Soft Bounces",
  "reports.spend": "Custo",
  "reports.this_month": "Este Mês",
  "reports.this_week_sun_today": "Esse Semana",
  "reports.this_year": "Este Ano",
  "reports.today": "Hoje",
  "reports.total": "Total",
  "reports.uniqueRecipients": "Destinatários Únicos",
  "reports.unsubscribed": "Cancelamentos",
  "reports.unsubscribed.singular": "Cancelamento",
  "reports.viewed": "Vistos",
  "reports.viewed.singular": "Visto",
  "reports.yesterday": "Ontem",
  "roles.accountOwner": "Dono da Conta",
  "roles.admin": "Administrador do Sistema",
  "roles.admins": "Administradores do Sistema",
  "roles.affiliate": "Afiliado",
  "roles.affiliates": "Afiliados",
  "roles.lead": "Lead",
  "roles.leads": "Leads",
  "roles.manager": "Gerente de Contas",
  "roles.managers": "Gerentes de Contas",
  "roles.operator": "Operador",
  "roles.operators": "Operadores",
  "roles.owner": "Administrador",
  "roles.owners": "Administradores",
  "roles.promoter": "Promotor",
  "roles.promoters": "Promotores",
  "roles.role": "Função",
  "roles.roles": "Funções",
  "roles.seller": "Vendedor",
  "roles.sellers": "Vendedores",
  "roles.webmaster": "Webmaster",
  "roles.webmasters": "Webmasters",
  "roulette.equal": "Distribuição Equitativa",
  "roulette.random": "Distribuição Randômica",
  "segmentations.add.leads": "Adicionar Leads a segmentação?",
  "segmentations.add.name": "Nome da nova Segmentação",
  "segmentations.add.new": "Criar nova Segmentação",
  "segmentations.add.new.text":
    "Você está prestes a criar uma nova segmentação com os leads listados, quer continuar?",
  "segmentations.dinamic": "Segmentação Dinâmica",
  "segmentations.dinamicAlert":
    "Esta Segmentação foi designada como Dinâmica quando foi criada. Não é possível alterar o tipo de Segmentação após a sua criação",
  "segmentations.dinamicConfigAlert":
    "Ao salvar pela primeira vez a Segmentação, esta será configurada como Estática ou Dinâmica e não será possível alterá-la posteriormente",
  "segmentations.dinamicLeads": "Leads na Segmentação",
  "segmentations.dinamicLeadsMatches":
    "Leads que coincidem com os critérios da Segmentação",
  "segmentations.dinamicType": "Dinâmica",
  "segmentations.static": "Segmentação Estática",
  "segmentations.staticAlert":
    "Esta Segmentação foi designada como Estática quando foi criada. Não é possível alterar o tipo de Segmentação após a sua criação",
  "segmentations.staticType": "Estática",
  "segmentations.title": "Segmentações",
  "sellers.roulette": "Roleta de Vendedores",
  "shortcodes.data:contractNumber": "Número do Contrato",
  "shortcodes.data:total": "Total do Pedido",
  "shortcodes.date": "Data de Criação",
  "shortcodes.ID": "Número",
  "shortcodes.items": "Itens do pedido",
  "shortcodes.modified": "Data de Modificação",
  "shortcodes.payment:gateway": "Forma de Pagamento",
  "shortcodes.sentDate": "Dia do Mês do Envio",
  "shortcodes.sentDay": "Dia da semana do envio",
  "shortcodes.sentFullDate": "Data do Envio",
  "shortcodes.sentMonth": "Mês do Envio",
  "shortcodes.sentPlusDate": "Data a partir do envio",
  "shortcodes.sentYear": "Ano do Envio",
  "shortcodes.tips.sentDate": "Ex: 31",
  "shortcodes.tips.sentDay": "Ex: segunda-feira",
  "shortcodes.tips.sentFullDate": "DD/MM/AAAA",
  "shortcodes.tips.sentMonth": "Ex: Março",
  "shortcodes.tips.sentYear": "Ex: 2020",
  "shortcodes.tips.type": "Pessoa Física ou Empresa",
  "shortcodes.type": "Tipo de Conta",
  "shotx.add.apiKeys": "Adicione suas chaves do ShotFlow Aqui",
  "shotx.apiKeys.saveApiKeysSucessfully": "Chaves salvas com sucesso",
  "shotx.broadcast.cancel": "Cancelar",
  "shotx.broadcast.cancel.dialog.description":
    "Você tem certeza de que deseja cancelar o envio da mensagem em massa?",
  "shotx.broadcast.cancel.dialog.title": "Cancelamento de Mensagem em Massa",
  "shotx.broadcast.edit": "Editando ",
  "shotx.broadcast.failed": "Não Entregues",
  "shotx.broadcast.indicators": "Indicadores",
  "shotx.broadcast.instagram.delivered":
    "Não haveram dados para o tipo instagram",
  "shotx.broadcast.instanceSelect": "Selecione uma instância",
  "shotx.broadcast.instanceSelectLabel": "Selecione ou busque uma instância",
  "shotx.broadcast.leadsQtd": "Quantidade de Contatos",
  "shotx.broadcast.leadsSelect": "Selecione os leads",
  "shotx.broadcast.leadsSelectLabel": "Selecione ou busque os leads",
  "shotx.broadcast.messageContent": "Conteúdo da Mensagem",
  "shotx.broadcast.messagePreview": "Prévia da Mensagem",
  "shotx.broadcast.messageSelect": "Selecione uma mensagem",
  "shotx.broadcast.messageSelectLabel": "Selecione ou busque uma mensagem",
  "shotx.broadcast.messageTo": "Para",
  "shotx.broadcast.noLeads": "Não foram encontrados leads para esta instância",
  "shotx.broadcast.replieds": "Respondidas",
  "shotx.broadcast.rules": "Regras de Envio",
  "shotx.broadcast.schedule": "Agendar Envio",
  "shotx.broadcast.schedule.cancel": "Cancelar Envio",
  "shotx.broadcast.scheduleDate": "Data e Hora de Envio",
  "shotx.broadcast.segmentationsSelect": "Selecione as segmentações",
  "shotx.broadcast.segmentationsSelectLabel":
    "Selecione ou busque as segmentações",
  "shotx.broadcast.segmetationsQtd": "Quantidade de Segmentações",
  "shotx.broadcast.selectAll": "Selecionar todos",
  "shotx.broadcast.selectInstance":
    "Selecione uma instância para carregar os Leads disponíveis",
  "shotx.broadcast.selectNone": "Limpar seleção",
  "shotx.broadcast.sendingStatus": "Status do Envio",
  "shotx.broadcast.sendLater": "Agendar Envio",
  "shotx.broadcast.sendNow": "Enviar Agora",
  "shotx.broadcast.sendTypeSelect": "Selecione a forma de envio",
  "shotx.broadcast.sendTypeSniper": "Acionar ShotFlow",
  "shotx.broadcast.sendTypeText": "Enviar Mensagem",
  "shotx.broadcast.sent": "Enviadas",
  "shotx.broadcast.sniperSelect": "Selecione um ShotFlow",
  "shotx.broadcast.sniperSelectLabel": "Selecione ou busque um ShotFlow",
  "shotx.broadcast.status.sent": "Enviado",
  "shotx.broadcast.tagsFilter": "Filtrar por Tags",
  "shotx.broadcast.title": "Envio de Mensagens em Massa",
  "shotx.broadcast.title.analytcs": "[%type] [%date_schedule] [%platform]",
  "shotx.broadcast.title.editing": "Editando [%type] [%date_schedule]",
  "shotx.broadcast.total": "Total",
  "shotx.broadcast.interval": "Intervalo entre mensagens",
  "shotx.broadcast.intervalQty": "Quantidade",
  "shotx.broadcast.intervalUnit": "Unidade",
  "shotx.chat.audioNotAvailable": "Áudio indisponível",
  "shotx.chat.discard": "Descartar",
  "shotx.chat.fileNotAvailable": "Arquivo indisponível",
  "shotx.chat.imageNotAvailable": "Imagem indisponível",
  "shotx.chat.send": "Enviar",
  "shotx.chat.sendMedia": "Enviar midia",
  "shotx.chat.videoNotAvailable": "Vídeo indisponível",
  "shotx.connectedNumber": "Número conectado",
  "shotx.instagram.instance.create.info":
    " Não é possível conectar a mesma conta do Instagram em mais de uma instância",
  "shotx.instagram.instance.title.placeholder":
    "Nome da Instância do Instagram",
  "shotx.instagram.noContact":
    "Vincule uma interação ao Lead para enviar mensagem.",
  "shotx.instagram.noIntegrations": "Nenhuma integração configurada",
  "shotx.instagram.notReady": "Instagram ainda não está pronto para uso",
  "shotx.instagram.onRefused": "Conexão recusada",
  "shotx.instagram.selectLead": "Selecione um lead",
  "shotx.instance.connect": "Conectar",
  "shotx.instance.connected": "Conectado",
  "shotx.instance.connectedSuccessfully": "Conectado com sucesso",
  "shotx.instance.connecting": "Conectando",
  "shotx.instance.create": "Criar instância",
  "shotx.instance.disconnect": "Desconectar",
  "shotx.instance.disconnected": "Desconectado",
  "shotx.instance.disconnectedInfo":
    "Desconectado. Por favor, conecte o QIPlus ao seu [%platform] e tente novamente.",
  "shotx.instance.disconnectedSuccessfully": "Desconectado com sucesso",
  "shotx.instance.disconnecting": "Desconectando",
  "shotx.instance.edit": "Editando [%instance]",
  "shotx.instance.interactions": "Interacões",
  "shotx.instance.interactions.notVinculated": "Nenhum lead vinculado",
  "shotx.instance.interactions.title": "Interacões de [%instance]",
  "shotx.instance.interactions.unvinculate": "Desvincular Lead",
  "shotx.instance.interactions.vinculate": "Vincular Lead",
  "shotx.instance.loading": "Carregando",
  "shotx.instance.noMessages": "Nenhuma mensagem",
  "shotx.instance.qrcode":
    "Faça a leitura do QrCode com o seu aplicativo do Whatsapp.",
  "shotx.instance.refused":
    "O tempo para conexão expirou, por favor tente novamente",
  "shotx.instance.saveChangesSuccessfully": "Alterações salvas com sucesso",
  "shotx.instance.willBeDeleted":
    "Ao eliminar a instância, todos os dados e mensagens serão movidos para a lixeira. Deseja continuar? Essa operação não pode ser desfeita",
  "shotx.module.title": "ShotX",
  "shotx.module.tooltip": "Chats integrados com o ShotX",
  "shotx.plataform.config": "Configurações",
  "shotx.plataform.title": "Plataformas",
  "shotx.plataform.whatsapp.title": "Whatsapp",
  "shotx.snipers.apiKeyToken": "Token de API",
  "shotx.snipers.form.apiKey": "Adicione seu Token de API",
  "shotx.snipers.form.keyword": "Palavra-chave",
  "shotx.snipers.form.keywordFinish": "Palavra para Finalizar ShotFlow",
  "shotx.snipers.form.keywordFinishPlaceholder":
    "Palavra para Finalizar Conversa com ShotFlow",
  "shotx.snipers.form.selectTrigger": "Selecionar gatilho",
  "shotx.snipers.form.selectTypeTrigger": "Selecionar tipo de Gatilho",
  "shotx.snipers.form.trigger": "Gatilho",
  "shotx.snipers.form.typeTrigger": "Tipo de Gatilho",
  "shotx.snipers.form.unknownMessage": "Mensagem Desconhecida",
  "shotx.snipers.form.unknownMessagePlaceholder":
    "Frase ao receber uma opção desconhecida",
  "shotx.snipers.form.withoutApiKey":
    "Token de Api necessária para atualização dos dados do Lead",
  "shotx.snipers.form.withoutTitle": "Favor informar o Id Publico do ShotFlow",
  "shotx.snipers.noApiKeyToken":
    "Nenhuma chave encontrada, cadastre suas chaves na aba Configurações",
  "shotx.snipers.nomeSniper": "Nome do ShotFlow (Bot)",
  "shotx.snipers.noSniper": "Não há ShotFlow criado neste Workspace!",
  "shotx.snipers.operator.contains": "Contém",
  "shotx.snipers.operator.endsWith": "Termina com",
  "shotx.snipers.operator.equals": "Igual",
  "shotx.snipers.operator.startsWith": "Começa com",
  "shotx.snipers.scheduledMessage": "Atendimento agendado com sucesso",
  "shotx.snipers.title": "ShotFlows",
  "shotx.snipers.typeOperador": "Tipo / Operador",
  "shotx.snipers.types.all": "Todos",
  "shotx.snipers.types.keyword": "Palavra-chave",
  "shotx.snipers.value": "Valor",
  "shotx.statuses.close": "Desconectado",
  "shotx.statuses.connecting": "Conectando",
  "shotx.statuses.open": "Conectado",
  "shotx.statuses.refused": "Recusado",
  "shotx.tabs.config": "Configurações",
  "shotx.tabs.ImportSniper": "Importar ShotFlow",
  "shotx.tabs.instance": "Instâncias",
  "shotx.tabs.sniper": "ShotFlow",
  "shotx.whatsapp.assignedToSniper": "Atendimento atribuido ao ShotFlow!",
  "shotx.whatsapp.assigningToSniper": "Atribuindo atendimento ao ShotFlow",
  "shotx.whatsapp.assignToSniper": "Atribuir atendimento ao ShotFlow",
  "shotx.whatsapp.associatedFieldSniper": "Variavel no ShotFlow",
  "shotx.whatsapp.associatedLeadFieldSniper": "Campo a atualizar no lead",
  "shotx.whatsapp.clickToEnd": "Encerrar atendimento",
  "shotx.whatsapp.clickToStart": "Iniciar",
  "shotx.whatsapp.ended": "Atendimento encerrado por @username em @date.",
  "shotx.whatsapp.instance.alert":
    "Alterar o nome da instância causará a perda do historico de mensagens.",
  "shotx.whatsapp.instance.body.title": "Instância",
  "shotx.whatsapp.instance.info": "Insira uma descrição para a instância.",
  "shotx.whatsapp.instance.title.info": "Informe o nome da instância",
  "shotx.whatsapp.instance.title.placeholder": "Nome da Instância do Whatsapp",
  "shotx.whatsapp.iWillAnswer": "Eu vou atender",
  "shotx.whatsapp.noContact":
    "Vincule um contato com celular válido para enviar mensagem.",
  "shotx.whatsapp.noIntegrations": "Nenhuma integração configurada",
  "shotx.whatsapp.noQuickMessages":
    "Não existe nenhuma mensagem rápida cadastrada.",
  "shotx.whatsapp.noSniper": "Não existe nenhum ShotFlow",
  "shotx.whatsapp.notReady": "Whatsapp ainda não está pronto para uso",
  "shotx.whatsapp.notStarted":
    "Você ainda não iniciou o atendimento, clique em iniciar para responder.",
  "shotx.whatsapp.qrcode.button.disconnect": "Desconectar",
  "shotx.whatsapp.qrcode.button.refresh": "Atualizar QrCode",
  "shotx.whatsapp.qrcode.info":
    "Faça a leitura do QrCode com o seu aplicativo do Whatsapp.",
  "shotx.whatsapp.qrcode.loading": "Gerando QrCode...",
  "shotx.whatsapp.request.createinstance.fail": "Falha ao criar a instância",
  "shotx.whatsapp.selectSniper": "Selecione um ShotFlow",
  "shotx.whatsapp.started": "Atendimento iniciado por @username em @date.",
  "shotx.whatsapp.status.connected":
    "Você está com um número conectado ao whatsapp.",
  "shotx.whatsapp.status.disconnected": "Não Conectado",
  "sidebar.": "",
  "sidebar.404": "404",
  "sidebar.500": "500",
  "sidebar.aboutUs": "Sobre nós",
  "sidebar.account": "Minha Conta",
  "sidebar.add": "Adicionar",
  "sidebar.addNew": "Criar",
  "sidebar.agency": "Agência",
  "sidebar.alerts": "Alertas",
  "sidebar.analytics": "Analytics",
  "sidebar.app": "App",
  "sidebar.applications": "Aplicações",
  "sidebar.blank": "Em branco",
  "sidebar.boxed": "Boxed",
  "sidebar.calendar": "Calendário",
  "sidebar.cart": "Cart",
  "sidebar.chat": "Chat",
  "sidebar.checkout": "Checkout",
  "sidebar.clients": "clientes",
  "sidebar.component": "Componentes",
  "sidebar.contact": "Contato",
  "sidebar.crm": "CRM",
  "sidebar.crypto": "Crypto",
  "sidebar.dashboard": "Dashboard",
  "sidebar.dateTimePicker": "Date & Time Picker",
  "sidebar.dropzone": "Dropzone",
  "sidebar.ecommerce": "Ecommerce",
  "sidebar.editor": "Editor",
  "sidebar.email": "Email",
  "sidebar.emails": "Emails",
  "sidebar.event": "Evento",
  "sidebar.events": "Eventos",
  "sidebar.extensions": "Extensions",
  "sidebar.faq(s)": "Faq(s)",
  "sidebar.features": "Recursos",
  "sidebar.feedback": "Feedback",
  "sidebar.forgotPassword": "Esqueci a senha",
  "sidebar.forms": "Formulários",
  "sidebar.funnel": "Funil de Vendas",
  "sidebar.funnels": "Funis de Vendas",
  "sidebar.gallery": "Galeria",
  "sidebar.general": "Geral",
  "sidebar.gettingStarted": "Getting Started",
  "sidebar.home": "Home",
  "sidebar.horizontal": "Horizontal",
  "sidebar.horizontalMenu": "Menu Horizontal",
  "sidebar.icons": "Ícones",
  "sidebar.imageCropper": "Ferramenta de Recorte",
  "sidebar.inbox": "Inbox",
  "sidebar.invoice": "Invoice",
  "sidebar.leads": "Leads",
  "sidebar.level1": "Level 1",
  "sidebar.level2": "Level 2",
  "sidebar.listAction": "Listar",
  "sidebar.lockScreen": "Lock Screen",
  "sidebar.login": "Login",
  "sidebar.mailbox": "Inbox",
  "sidebar.mailboxes": "Inbox",
  "sidebar.mailing": "Mailing",
  "sidebar.miscellaneous": "Variados",
  "sidebar.model": "Modelo",
  "sidebar.models": "Modelos",
  "sidebar.module": "Módulo",
  "sidebar.modules": "Módulos",
  "sidebar.multilevel": "Multilevel",
  "sidebar.multiLevel": "MultiLevel",
  "sidebar.news": "Novidades",
  "sidebar.pages": "Páginas",
  "sidebar.pipeline": "Pipeline",
  "sidebar.pipelines": "Pipelines",
  "sidebar.plans.dashboard": "Relatório Financeiro",
  "sidebar.pricing": "Preços",
  "sidebar.product": "Produto",
  "sidebar.products": "Produtos",
  "sidebar.progress": "Progresso",
  "sidebar.projectDetail": "detalhes do projeto",
  "sidebar.projects": "projetos",
  "sidebar.promo": "Promo",
  "sidebar.register": "Registro",
  "sidebar.report": "Relatório",
  "sidebar.reports": "Relatórios",
  "sidebar.reports.automations.ads": "Google Ads",
  "sidebar.saas": "SAAS",
  "sidebar.segmentationsGroup": "Segmentações",
  "sidebar.session": "Sessão",
  "sidebar.shop": "Shop",
  "sidebar.shopGrid": "Shop Grid",
  "sidebar.shopList": "Shop List",
  "sidebar.sublevel": "Sublevel",
  "sidebar.tables": "Tabelas",
  "sidebar.terms&Conditions": "Termos & Condições",
  "sidebar.toDo": "ToDo",
  "sidebar.toggle": "Mostrar/Ocultar Barra Lateral",
  "sidebar.tools": "Ferramentas",
  "sidebar.user": "Usuário",
  "sidebar.userList": "Lista de usuários",
  "sidebar.userManagement": "User Management",
  "sidebar.userProfile": "Perfil de Usuário",
  "sidebar.users": "Usuários",
  "sidebar.videoPlayer": "Player de Vídeo",
  "sniper.botAvailable": "Opção disponível após conectar a sua instância",
  "sniper.botDisabled": "Bot desabilitado",
  "sniper.botEnabled": "Bot habilitado",
  "sniper.botId": "ID público do ShotFlow",
  "sniper.module.title": "ShotFlow",
  "sniper.module.tooltip": "Automatização de Conversas",
  "sounds.off": "Desativar sons",
  "sounds.on": "Ativar sons",
  "stats.adsStats": "Métricas de Anúncios",
  "stats.automationsConvertions": "Converões em Automações",
  "stats.automationsFired": "Disparos de Automações",
  "stats.average": "Média",
  "stats.CAC": "Custo para aquisição de clientes",
  "stats.campaignsFired": "Disparos de Campanhas",
  "stats.campaignStats": "Métricas da Campanha",
  "stats.cap": "Captação",
  "stats.conversionAvg": "Valor Médio de Conversão",
  "stats.conversionCost": "Custo de Conversão",
  "stats.conversionCycle": "Ciclo de Vendas",
  "stats.conversionCycleAvgTime": "Tempo Médio do Ciclo de Vendas",
  "stats.conversionRate": "Taxa de Conversão",
  "stats.conversions": "Conversões",
  "stats.conversionsChart": "Estatísticas de Negócios",
  "stats.conversionsCount": "Pedidos Realizados",
  "stats.dealInsWonStage": "Negócios Marcados como Ganhos",
  "stats.dealsAdded": "Negócios Novos",
  "stats.dealsAddedValue": "Valor dos Negócios Novos",
  "stats.dealsLost": "Negócios Perdidos",
  "stats.dealsLostRate": "Taxa de Negócios Perdidos",
  "stats.dealsLostValue": "Valor dos Negócios Perdidos",
  "stats.dealsStats": "Métricas de Negócios",
  "stats.dealsWon": "Negócios Ganhos",
  "stats.dealsWonRate": "Taxa de Negócios Fechados",
  "stats.dealsWonValue": "Valor dos Negócios Ganhos",
  "stats.emailsClickRate": "Taxa de Cliques",
  "stats.emailsOpenCount": "Volume de Aberturas",
  "stats.emailsOpenRate": "Taxa de Abertura",
  "stats.emailsSentCount": "Volume de Envios",
  "stats.emptyMsg": "Não há dados para o período por enquanto",
  "stats.formsSubmitions": "Formulários Preenchidos",
  "stats.funnelsConvertions": "Converões em Funis",
  "stats.generated": "Gerados",
  "stats.geoLocation": "Geolocalização",
  "stats.investmentsTotal": "Investimento Total",
  "stats.landingSessions": "Sessões em Landing Pages",
  "stats.landingStats": "Métricas de Landing Pages",
  "stats.leadsBaseSize": "Tamanho da Base",
  "stats.leadsCap": "Captação de Leads",
  "stats.leadsConverted": "Clientes com Conversões",
  "stats.leadsDailyCap": "Captação Diária",
  "stats.leadsReached": "Leads Atingidos",
  "stats.leadsRegistered": "Captação Total",
  "stats.lost": "Perdidos",
  "stats.mailingStats": "Métricas de Envios",
  "stats.net": "Líquido",
  "stats.netRevenue": "Receita Líquida",
  "stats.new": "Novos",
  "stats.opportunities": "Oportunidades",
  "stats.QtdDeals": "Quantidade de Negócios",
  "stats.ROI": "Retorno do Investimento",
  "stats.sessions": "Sessões",
  "stats.sessionsCount": "Total de Visitas",
  "stats.sessionsIn": "Sessões em",
  "stats.teamsRanking": "Ranking de Equipe",
  "stats.teamsStats": "Métricas de Equipes",
  "stats.ticketsAddedCount": "Pedidos Realizados",
  "stats.ticketsAverage": "Ticket Médio",
  "stats.ticketsPerDeal": "Pedidos por Negócio",
  "stats.ticketsTotal": "Receita Acumulada",
  "stats.tooltip.CAC":
    "Customer Acquisition Costs (ou custo para aquisição de clientes), define qual é o valor gasto pela empresa para transformar um lead em um comprador ou em um usuário dos serviços oferecidos. Fórmula de Cálculo: Custo total para aquisição de clientes / Total de novos clientes = CAC",
  "stats.tooltip.LTV":
    "Lifetime Value (LTV) identifica o valor que cada cliente deixou em sua empresa ao longo do período que ele consumiu seus produtos e serviços. Fórmula de Cálculo: (Valor Médio de uma Venda) X (Média Tempo de Retenção em meses ou anos para um cliente típico) = LTV",
  "stats.tooltip.ROI":
    "Return On Investment (retorno sobre o investimento) é um indicador que permite às empresas saberem quanto ganhou ou perdeu com seus investimentos. Fórmula de Cálculo: (Retorno do investimento – custo do investimento) / custo do investimento = ROI",
  "stats.total": "Total",
  "stats.traffic": "Tráfego",
  "stats.trafficSources": "Origens de Tráfego",
  "stats.uniqueLeads": "Leads Únicos",
  "stats.uniqueUsers": "Usuários Únicos",
  "stats.uniqueVisitors": "Visitantes Únicos",
  "stats.valueAverage": "Valor Médio",
  "stats.visitors": "Visitantes",
  "stats.won": "Ganhos",
  "stats.wonCycle": "Ciclo do Negócio",
  "stats.wonCycleAvgTime": "Tempo Médio do Ciclo do Negócio",
  "stats.wonCycleClosingTime": "Tempo Médio de Conclusão",
  "statuses.active": "Ativo",
  "statuses.active.fem": "Ativa",
  "statuses.draft": "Rascunho",
  "statuses.draft.fem": "Rascunho",
  "statuses.inactive": "Inativo",
  "statuses.inactive.fem": "Inativa",
  "statuses.model": "Modelo",
  "statuses.publish": "Publicado",
  "statuses.publish.fem": "Publicada",
  "statuses.scheduled": "Agendado",
  "statuses.scheduled.fem": "Agendada",
  "statuses.send": "Enviado",
  "statuses.send.fem": "Enviada",
  "statuses.sending": "Enviando",
  "statuses.sending.fem": "Enviando",
  "statuses.sent": "Enviado",
  "statuses.sent.fem": "Enviada",
  "statuses.trash": "Lixeira",
  "statuses.trash.fem": "Lixeira",
  "stock.available": "Disponíveis",
  "stock.initial": "Para a Campanha",
  "stock.label": "Estoque",
  "stock.real": "Estoque Físico",
  "stock.used": "Utilizados",
  "styles.backgroundColor": "Cor de Fundo",
  "styles.borderColor": "Cor da Borda",
  "styles.borderWidth": "Espessura da Borda",
  "styles.buttonBackground": "Cor de Fundo do Botão",
  "styles.buttonColor": "Cor do Texto do Botão",
  "styles.buttonOptionsLarge": "Grande",
  "styles.buttonOptionsLeft": "Esquerda",
  "styles.buttonOptionsMedium": "Médio",
  "styles.buttonOptionsMiddle": "Centro",
  "styles.buttonOptionsRight": "Direita",
  "styles.buttonOptionsSmall": "Pequeno",
  "styles.buttonPosition": "Posição do Botão",
  "styles.buttonText": "Texto do Botão",
  "styles.customCSS": "CSS Personalizado",
  "styles.fieldBackground": "Cor de Fundo do Campo",
  "styles.fieldBorderColor": "Cor da Borda do Campo",
  "styles.fieldColor": "Cor do Campo",
  "styles.labelColor": "Cor do Rótulo",
  "styles.sizeButton": "Tamanho do Botão",
  "subscription.billing": "Faturação",
  "subscription.boleto": "Boleto Bancário",
  "subscription.canceled": "Cancelado",
  "subscription.credit_card": "Cartão de Crédito",
  "subscription.ended": "Finalizado",
  "subscription.overview.amount": "Valor",
  "subscription.overview.bankSlips": "Boletos Bancários",
  "subscription.overview.canceled": "Cancelada",
  "subscription.overview.canceleds": "Canceladas",
  "subscription.overview.count": "Quantidade",
  "subscription.overview.creditCards": "Cartões de Crédito",
  "subscription.overview.customer": "Cliente",
  "subscription.overview.dailyRevenues": "Receita Diária",
  "subscription.overview.geographicDistribution": "Distribuição Geográfica",
  "subscription.overview.list": "Assinaturas",
  "subscription.overview.monthlyRevenues": "Receita Mensal",
  "subscription.overview.paid": "Pago",
  "subscription.overview.paids": "Pagas",
  "subscription.overview.paymentMethod": "Forma de Pagamento",
  "subscription.overview.period": "Período",
  "subscription.overview.plan": "Plano",
  "subscription.overview.plansTotals": "Totais por Plano",
  "subscription.overview.state": "Estado",
  "subscription.overview.status": "Status",
  "subscription.overview.statusDistribution": "Distribuição por Status",
  "subscription.overview.subscriptionsEvolution": "Evolução de Assinaturas",
  "subscription.overview.total": "Assinaturas ativas",
  "subscription.overview.totalRevenues": "Receita Total",
  "subscription.overview.unpaid": "Pendente",
  "subscription.overview.unpaids": "Pendentes",
  "subscription.paid": "Pago",
  "subscription.pending_payment": "Pendente",
  "subscription.status": "Status da Assinatura",
  "subscription.trialing": "Trial",
  "subscription.unpaid": "Não pago",
  "tables.loadingText": "Carregando...",
  "tables.nextText": "Próxima",
  "tables.noDataText": "Não há items a mostrar",
  "tables.ofText": "de",
  "tables.pageText": "Página",
  "tables.previousText": "Anterior",
  "tables.rowsText": "",
  "tables.summaryText": "Página {from} de {count}",
  "tags.dealTags": "Tags do Negócio",
  "tags.leadTags": "Tags do Lead",
  "task.add": "Nova Tarefa",
  "task.clone": "Clonar Tarefa",
  "task.create": "Criar Tarefa",
  "task.edit": "Editar Tarefa",
  "tasklists.label.importance": "Peso",
  "tasklists.label.title": "Título",
  "tasklists.task_question.import": "Importar Questionario",
  "tasks.clone": "Clonar Tarefas",
  "tasks.edit": "Editar Tarefas",
  "texts.actions": "Ações",
  "texts.AddItemsToStartActions": "Arraste aqui as ações",
  "texts.AddItemsToStartTriggers": "Arraste aqui os disparadores",
  "texts.addNewPost": "Criar [%s]",
  "texts.addVideoPoster": "Adicionar Poster ao Vídeo",
  "texts.affiliateHomeLink": "Link da Home de Afiliado",
  "texts.affiliateLink": "Link de Afiliado",
  "texts.allActions": "Todas as Ações",
  "texts.alreadyHaveAccount": "Já possui uma conta?",
  "texts.alreadyHavingAccountSignIn": "Já possui uma conta? Acesse",
  "texts.and": "e",
  "texts.attachments": "Anexos",
  "texts.banner": "Banner",
  "texts.banners": "Banners",
  "texts.bannersKit": "Kit de Banners",
  "texts.campaign": "Campanha",
  "texts.cannotBeUndone": "Esta ação não pode ser desfeita",
  "texts.changeMyPlan": "Mudar meu plano",
  "texts.check-in": "check-in",
  "texts.checkin": "Check-in",
  "texts.checkin.plural": "Check-ins",
  "texts.choices": "Escolhas",
  "texts.chooseAnotherPlan": "Escolher outro plano",
  "texts.chooseYourPlan": "Escolha o seu plano",
  "texts.clickHereToSetup": "Clique aqui para configurar",
  "texts.clickHereToStart": "Clique aqui para iniciar",
  "texts.column": "Coluna",
  "texts.columns": "Colunas",
  "texts.commaSeparatedEmails": "Endereços de email separados por vírgula",
  "texts.commaSeparatedTags": "Tags separadas por vírgula",
  "texts.comments": "Comentários",
  "texts.completed": "Completo",
  "texts.confirmAllFieldsRemoval": "Deseja eliminar todos os campos?",
  "texts.confirmed": "Confirmado",
  "texts.confirmed.plural": "Confirmados",
  "texts.confirmFieldRemoval": "Deseja eliminar este campo?",
  "texts.content": "Conteúdo",
  "texts.contentType": "Tipo de Conteúdo",
  "texts.conversion": "Conversão",
  "texts.conversionGoals": "Metas de Conversão",
  "texts.conversions": "Conversões",
  "texts.copiedToClipboard": "Copiado para a área de transferência",
  "texts.copy": "Cópia",
  "texts.copyAffiliateHomeLink": "Copiar link da Home de Afiliado",
  "texts.copyAffiliateLink": "Copiar Link de Afiliado",
  "texts.copyEmbedCode": "Copiar código HTML para Embed",
  "texts.copyImageUrl": "Copiar a URL da Imagem",
  "texts.copyParentLink": "Copiar Link da Matriz",
  "texts.copyToClipboard": "Copiar",
  "texts.copyVideoUrl": "Copiar a URL do Vídeo",
  "texts.count": "Contagem",
  "texts.customBanners": "Banners Personalizados",
  "texts.customer": "Cliente",
  "texts.customers": "Clientes",
  "texts.customizeYourPlan": "Personalize o seu plano",
  "texts.customVideos": "Vídeos Personalizados",
  "texts.data": "Dados",
  "texts.date": "Data",
  "texts.dateRegistered": "Data de Registro",
  "texts.dates": "Datas",
  "texts.deadline": "Deadline",
  "texts.dealTitle": "Título do Negócio",
  "texts.description": "Descrição",
  "texts.discardImage": "Descartar a Imagem",
  "texts.discardVideo": "Descartar o Vídeo",
  "texts.document": "Documento",
  "texts.documents": "Documentos",
  "texts.dontHaveAccount": "Não possui uma conta ainda?",
  "texts.dropItemsHere": "Arraste e solte items aqui",
  "texts.email": "Email",
  "texts.emails": "Emails",
  "texts.embed": "Embed",
  "texts.embedCreated": "Embed criado com sucesso!",
  "texts.enable": "Ativar",
  "texts.end": "Fim",
  "texts.enterPasswords": "Senhas",
  "texts.enterYourEmail": "Seu Email",
  "texts.enterYourName": "Seu Nome",
  "texts.enterYourPassword": "Insira sua Senha",
  "texts.equal_mode": "Equitativa",
  "texts.fetchingItems": "Carregando...",
  "texts.fillActionData": "Complete os campos da ação",
  "texts.filterAction": "Filtrar",
  "texts.filterBy": "Filtrar por",
  "texts.filterChoices": "Filtrar Escolhas",
  "texts.filterValues": "Filtrar Selecionados",
  "texts.forgotPassword": "Esqueci a senha",
  "texts.goal": "Meta",
  "texts.goalName": "Nome da Meta",
  "texts.goals": "Metas",
  "texts.goalTarget": "Atribuir Meta a",
  "texts.goalType": "Tipo de Meta",
  "texts.graphicPieces": "Peças Gráficas",
  "texts.hi": "Olá",
  "texts.historial": "Histórico",
  "texts.if": "Se",
  "texts.image": "Imagem",
  "texts.images": "Imagens",
  "texts.importBannersKit": "Importar Kit de Banners Default",
  "texts.importSuccess": "Importação realizada com sucesso",
  "texts.in": "em",
  "texts.informYourDomain": "Informe seu domínio",
  "texts.insertTitle": "Insira o título",
  "texts.instructions": "Instruções",
  "texts.investmentName": "Nome do Investimento",
  "texts.investments": "Investimentos",
  "texts.investmentsOnPeriod": "Investimentos no Período",
  "texts.lead": "Lead",
  "texts.leads": "Leads",
  "texts.leadsGoals": "Metas de Leads",
  "texts.leaveBlank": "Deixe em branco se não aplica",
  "texts.listAction": "Listar",
  "texts.listAll": "Listar todos",
  "texts.loading": "Carregando...",
  "texts.manager": "Gerente de Contas",
  "texts.managers": "Gerentes de Contas",
  "texts.mktBanner": "Banner Promocional",
  "texts.mktBanners": "Banners Promocionais",
  "texts.mktMaterial": "Material Promocional",
  "texts.mktVideo": "Vídeo Promocional",
  "texts.mktVideos": "Vídeos Promocionais",
  "texts.montlyPlan": "Plano Mensal",
  "texts.myPlan": "Meu plano",
  "texts.new": "Novo",
  "texts.newDeal": "Novo Negócio",
  "texts.newPost": "Novo Item",
  "texts.newRegisters": "Novos",
  "texts.newUser": "Novo usuário",
  "texts.noItemsAvailable": "Nenhum item disponível",
  "texts.noItemsSelected": "Nenhum item selecionado",
  "texts.none": "Nenhum",
  "texts.none.f": "Nenhuma",
  "texts.noOperationsToPerform": "Não há operaçãoes a realizar",
  "texts.option": "Opção",
  "texts.optional": "Opcional",
  "texts.options": "Opções",
  "texts.or": "ou",
  "texts.orSigninWith": "ou faça login com",
  "texts.other": "Outro",
  "texts.otherActions": "Outras Ações",
  "texts.others": "Outros",
  "texts.parentLink": "Tree Business Link",
  "texts.parentTitle": "Tree Business",
  "texts.participant": "Participante",
  "texts.participants": "Participantes",
  "texts.passwordResetEmailSent": "Email enviado com sucesso",
  "texts.payment": "Pagamento",
  "texts.payments": "Pagamentos",
  "texts.platform": "Plataforma",
  "texts.platforms": "Plataformas",
  "texts.preferences": "Preferências",
  "texts.privateToken": "Token Privado",
  "texts.processing": "Processando",
  "texts.promoter": "Promotor",
  "texts.promoters": "Promotores",
  "texts.qiplusUrlType": "Tipo de Página",
  "texts.qiusers": "Usuários",
  "texts.random_mode": "Randômica",
  "texts.redirect": "Redirecionamento",
  "texts.redirectAction": "Redirecionar",
  "texts.redirectType": "Tipo de Redirecionamento",
  "texts.refreshToken": "Refresh Token",
  "texts.relate": "Relacionar",
  "texts.related": "Relacionados",
  "texts.reloadSection": "Recarregar",
  "texts.removeImage": "Eliminar a Imagem",
  "texts.removeImageSize": "Eliminar este Tamanho de Imagem",
  "texts.removeVideo": "Eliminar o Vídeo",
  "texts.removeVideoSize": "Eliminar este Tamanho de Vídeo",
  "texts.resetPassword": "Redefinir Senha",
  "texts.result": "Resultado",
  "texts.results": "Resultados",
  "texts.script": "Script",
  "texts.scripts": "Scripts",
  "texts.select": "Selecione",
  "texts.select.intance": "Selecione sua instância",
  "texts.select.quickMessage": "Selecione uma mensagem rápida",
  "texts.selectAnAction": "Selecione uma ação",
  "texts.selectAnItem": "Selecione um item",
  "texts.selectAStage": "Selecione uma etapa",
  "texts.selectATrigger": "Selecione um disparador",
  "texts.selectDocument": "Selecione um documento",
  "texts.selected": "Selecionado",
  "texts.selectedPlural": "Selecionados",
  "texts.selectImage": "Selecionar imagem",
  "texts.selectOwner": "Selecione uma conta",
  "texts.seller": "Vendedor",
  "texts.sellers": "Vendedores",
  "texts.shortlink": "Shortlink",
  "texts.shortlinkCreated": "Shortlink criado com sucesso!",
  "texts.source": "Fonte",
  "texts.sources": "Fontes",
  "texts.stage": "Etapa",
  "texts.stages": "Etapas",
  "texts.start": "Início",
  "texts.status": "Status",
  "texts.store": "Loja",
  "texts.stores": "Lojas",
  "texts.submitedScripts": "Scripts na página de confirmação",
  "texts.subscribed": "Inscrito",
  "texts.subscribed.plural": "Inscritos",
  "texts.tags": "Tags",
  "texts.tasklistLost": "Perdidos",
  "texts.tasklistWon": "Ganhos",
  "texts.termsAndConditions": "Termos e Condições",
  "texts.ticketItems": "Itens do Pedido",
  "texts.token": "Token",
  "texts.tracking": "Rastreamento",
  "texts.treeBusinessTitle": "Tree Business",
  "texts.trialDays": "Dias de Trial",
  "texts.trigger": "Disparador",
  "texts.triggers": "Disparadores",
  "texts.tutorial": "Tutorial",
  "texts.type": "Tipo",
  "texts.unit": "Unidade",
  "texts.units": "Unidades",
  "texts.updated": "Atualizado",
  "texts.updated.plural": "Atualizados",
  "texts.updateDeal": "Editar Negócio",
  "texts.updatedRegisters": "Atualizados",
  "texts.updateUser": "Editar usuário",
  "texts.updateYourPlan": "Atualize o seu plano",
  "texts.userGoodbye": "Até a próxima!",
  "texts.userLogoutSuccess": "Até a próxima!",
  "texts.usersIncluded": "Usuários Incluídos",
  "texts.userWelcome": "Bem-vindo",
  "texts.value": "Valor",
  "texts.video": "Vídeo",
  "texts.videoPoster": "Poster",
  "texts.wantThisPlan": "Quero este plano",
  "texts.yearlyPlan": "Plano Anual",
  "themeOptions.appSettings": "App Settings",
  "themeOptions.boxLayout": "Box Layout",
  "themeOptions.darkMode": "Modo Escuro",
  "themeOptions.gridLayout": "Layout: Grid",
  "themeOptions.listLayout": "Layout: Lista",
  "themeOptions.miniSidebar": "Menu Compacto",
  "themeOptions.rtlLayout": "Rtl Layout",
  "themeOptions.sidebarBackgroundImages": "Sidebar Background Images",
  "themeOptions.sidebarDark": "Dark",
  "themeOptions.sidebarImage": "Sidebar Image",
  "themeOptions.sidebarLight": "Light",
  "themeOptions.sidebarOverlay": "Sidebar Overlay",
  "themeOptions.themeColor": "Cor do Tema",
  "time.after": "Depois",
  "time.afterStart": "Depois do início",
  "time.before": "Antes",
  "time.beforeStart": "Antes do início",
  "time.days": "dias",
  "time.deadline": "Deadline",
  "time.hours": "horas",
  "time.invalidDateMessage": "Data inválida",
  "time.maxDateMessage": "A data é maior ao máximo permitido",
  "time.minDateMessage": "A data é menor do que o mínimo permitido",
  "time.minutes": "minutos",
  "time.onadd": "Após ser adicionado",
  "time.onadd.fem": "Após ser adicionada",
  "time.oncreate": "Após a criação",
  "time.oncreate.fem": "Após a criação",
  "time.time": "Horário",
  "tips.saveOrUseOnlyInThisForm":
    "Você pode utilizar este campo somente neste formulário ou salvá-lo no banco de dados",
  "triggers.added": "Novo",
  "triggers.added_as_participant": "Participante Adicionado",
  "triggers.added_to_funnel": "Entrada no funil",
  "triggers.added_to_segmentation": "Adicionado à segmentação",
  "triggers.added_to_store": "Adicionado à loja",
  "triggers.added_via_integration": "Adicionado via integração",
  "triggers.addedAsParticipant": "Foi adicionado como participante",
  "triggers.addedToFunnel": "Entrada no funil",
  "triggers.addedToSegmentation": "Foi adicionado à segmentação",
  "triggers.addedToStore": "Foi adicionado à loja",
  "triggers.addedViaIntegration": "Foi adicionado através da integração",
  "triggers.addFromShotX": "Lead adicionado através do ShotX",
  "triggers.ageRange": "Por Range de Idade",
  "triggers.answeredAQuestionnaire": "Respondeu ao questionário",
  "triggers.birthdayMonth": "Por Mês de Aniversário",
  "triggers.bought": "Compra",
  "triggers.boughtAProduct": "Adquiriu o produto",
  "triggers.canceled": "Cancelado",
  "triggers.cep": "Por range de CEP",
  "triggers.checkedIn": "Fez check-in",
  "triggers.city": "Por cidade",
  "triggers.clickedAnEmail": "Clicou no Email",
  "triggers.completed": "Completo",
  "triggers.confirmed_participant": "Participação Confirmada",
  "triggers.confirmedParticipation": "Confirmou a participação",
  "triggers.converted": "Conversão",
  "triggers.convertedInFunnel": "Converteu no funil",
  "triggers.country": "Por pais",
  "triggers.currentStageInFunnel": "Está na etapa",
  "triggers.deals_added": "Negócios Novos",
  "triggers.didNotClickAnEmail": "Não clicou no email",
  "triggers.didNotOpenEmail": "Não abriu o email",
  "triggers.didntCheckin": "Não fex check-in",
  "triggers.filledAForm": "Preencheu o formulário",
  "triggers.fired": "Disparada",
  "triggers.gender": "Por Gênero",
  "triggers.impression": "Impressão",
  "triggers.interaction_added": "Interação vinculada a um Lead",
  "triggers.leadGenerated": "Lead Captado",
  "triggers.leads_added": "Leads Captados",
  "triggers.lost": "Negócios Perdidos",
  "triggers.movedToLost": "Marcado como Perdido",
  "triggers.movedToWon": "Marcado como Ganho",
  "triggers.neighborhood": "Por bairro",
  "triggers.neverBought": "Não comprado",
  "triggers.neverBoughtAProduct": "Nunca adquiriu o produto",
  "triggers.neverConverted": "Nunca Converteu",
  "triggers.notDealExistActive": "Não existe negócio ativo",
  "triggers.openedAnEmail": "Abriu o Email",
  "triggers.played": "Reproduções",
  "triggers.progressed": "Progressão no funil",
  "triggers.progressedInFunnel": "Progrediu no funil",
  "triggers.range": "Defina o intervalo",
  "triggers.receivedAMessage": "A cada Mensagem Recebida",
  "triggers.receivedAnEmail": "Recebeu o Email",
  "triggers.regressed": "Regressão no funil",
  "triggers.removed": "Removido",
  "triggers.removed_from_participants": "Removido dos participantes",
  "triggers.removed_from_segmentation": "Removido da segmentação",
  "triggers.removed_from_store": "Removido da loja",
  "triggers.removedFromarticipants": "Foi removido dos participantes",
  "triggers.removedFromSegmentation": "Foi removido da segmentação",
  "triggers.removedFromStore": "Foi removido da loja",
  "triggers.stage": "Está na etapa",
  "triggers.stageIn": "Ingresso na etapa",
  "triggers.stageInFunnel": "Ingressou na etapa",
  "triggers.stageOut": "Saída da etapa",
  "triggers.stageOutFunnel": "Saiu da etapa",
  "triggers.state": "Por por estado",
  "triggers.submited": "Form Enviado",
  "triggers.tag_added": "Etiqueta adicionada",
  "triggers.tag_removed": "Etiqueta removida",
  "triggers.tagAdded": "A etiqueta foi adicionada",
  "triggers.tagHasNot": "Não possui a etiqueta",
  "triggers.tagHasTag": "Possui a etiqueta",
  "triggers.tagRemoved": "A etiqueta foi removida",
  "triggers.tasklistCompleted": "Completou a lista de tarefas",
  "triggers.ticketCancelled": "Ticket Cancelado",
  "triggers.ticketGenerated": "Ticket Gerado",
  "triggers.tickets_canceled": "Cancelado",
  "triggers.triggerCol": "Grupo de Condições",
  "triggers.unconfirmed_participant": "Participação Desconfirmada",
  "triggers.unconfirmedParticipation": "Desconfirmou a participação",
  "triggers.undidCheckin": "O check-in foi cancelado",
  "triggers.unload": "Saída da Página",
  "triggers.updated": "Atualizado",
  "triggers.updated_via_integration": "Atualizado via integração",
  "triggers.updatedViaIntegration": "Foi atualizado através da integração",
  "triggers.viewed": "Visto",
  "triggers.visited": "Visita",
  "triggers.won": "Negócios Ganhos",
  "videos.customVideoUrl": "URL do Vídeo",
  "videos.dailymotionVideo": "Vídeo do Dailymotion",
  "videos.externalVideo": "URL de Vídeo Hospedado",
  "videos.facebookVideo": "Vídeo do Facebook",
  "videos.fileVideo": "Arquivo de Vídeo",
  "videos.twitchVideo": "Vídeo do Twitch",
  "videos.videoProvider": "Provedor do Vídeo",
  "videos.vimeoVideo": "Vídeo do Vimeo",
  "videos.youtubeVideo": "Vídeo do Youtube",
  "whatsapp.authenticated": "Autenticado",
  "whatsapp.connected": "Conectado",
  "whatsapp.connectedAs": "Conectado como",
  "whatsapp.connectQiplus": "Conecte o QIPlus com sua conta do WhatsApp",
  "whatsapp.createNewAccount": "Criar nova conta",
  "whatsapp.deleted": "Mensagem apagada",
  "whatsapp.edited": "Editada",
  "whatsapp.fullMobileNumber": "Número do celular com código de país e DDD",
  "whatsapp.generatingQrCode": "Gerando QR Code...",
  "whatsapp.inputMessage.placeholder": "Digite uma mensagem",
  "whatsapp.inputMessage.tooltip":
    "Se desejar, utilize [Ctrl + Enter] para enviar.",
  "whatsapp.me": "Eu",
  "whatsapp.message.send.fail": "Falha ao enviar.",
  "whatsapp.message.send.resend": "Clique para tentar reenviar esta mensagem.",
  "whatsapp.message.send.resend.fail": "Falha ao tentar reenviar mensagem.",
  "whatsapp.send": "Enviar",
  "whatsapp.settingYourAccount": "Preparando sua conta...",
  "whatsapp.successfullyConnectedQiplus":
    "Parabéns! O QIPlus está conectado à sua conta do WhatsApp",
  "whatsapp.waitingForQrCodeScan":
    "Abra o WhatsApp em seu celular e faça scan do QR Code para iniciar sessão",
  "widgets.aboutUs": "About Us",
  "widgets.AcceptorrRejectWithin": "Accept or reject within",
  "widgets.action": "Ação",
  "widgets.ActionsBuilder": "Selecione as ações",
  "widgets.ActionsSelector": "Selecione",
  "widgets.activeUsers": "Usuários Ativos",
  "widgets.activitis": "Atividades",
  "widgets.activity": "Atividade",
  "widgets.activityBoard": "Activity Board",
  "widgets.additionalContent": "Additional Content",
  "widgets.addNew": "Criar",
  "widgets.addToCart": "Adicionar ao Pedido",
  "widgets.admin": "Admin",
  "widgets.adminTheme": "Admin Theme",
  "widgets.advanced": "Avançado",
  "widgets.advancedGridLists": "Advanced Grid Lists",
  "widgets.advancedSearch": "Pesquisa Avançada",
  "widgets.agenda": "Agenda",
  "widgets.alertDialog": "Alert Dialog",
  "widgets.alertDismiss": "Alert Dismiss",
  "widgets.alertsWithIcons": "Alerts With Icons",
  "widgets.alertsWithLink": "Alerts With Link",
  "widgets.all": "Todos",
  "widgets.alreadyHavingAccountLogin": "Already Having Account Login",
  "widgets.anchorPlayGround": "Anchor Play Ground",
  "widgets.animatedSlideDialogs": "Animated Slide Dialogs",
  "widgets.anotherLink": "Another Link",
  "widgets.api": "Api",
  "widgets.app": "App",
  "widgets.appBarsWithButtons": "App Bars With Buttons",
  "widgets.appearOrder": "Ordem de Aparição",
  "widgets.apply": "Apply",
  "widgets.appNotifications": "App Notifications",
  "widgets.approve": "Aprovado",
  "widgets.areaChart": "Area Chart",
  "widgets.assignTeam": "Designar Equipe",
  "widgets.author": "Editor",
  "widgets.autoAssignTeam": "Designar Equipe Automaticamente",
  "widgets.autoComplete": "Auto Complete",
  "widgets.backend": "Backend",
  "widgets.backgroundVarient": "Background Varient",
  "widgets.badgeLinks": "Badge Links",
  "widgets.badgeWithHeadings": "Badge With Headings",
  "widgets.bandwidthUse": "Bandwidth Use",
  "widgets.barChart": "Bar Chart",
  "widgets.baseConfig": "Base Config",
  "widgets.basic": "Básico",
  "widgets.basicAlert": "Basic Alert",
  "widgets.basicCalendar": "Basic Calendar",
  "widgets.basicCalender": "Basic Calender",
  "widgets.basicTab": "Basic Tab",
  "widgets.basicTable": "Basic Table",
  "widgets.booking": "Booking",
  "widgets.bounced": "Bounced",
  "widgets.browse": "Browse",
  "widgets.bubbleChart": "Bubble Chart",
  "widgets.buffer": "Buffer",
  "widgets.buttonNavigation": "Button Navigation",
  "widgets.buttonNavigationWithNoLabel": "button Navigation With No Label",
  "widgets.buttonOutline": "Button Outline",
  "widgets.buttonSize": "Button Size",
  "widgets.buttonState": "Button State",
  "widgets.buttonWithIconAndLabel": "Button With Icon And Label",
  "widgets.buyMore": "Continuar comprando",
  "widgets.byDay": "Por dia",
  "widgets.byMonth": "Por mês",
  "widgets.byWeek": "Por semana",
  "widgets.byYear": "Por ano",
  "widgets.campaignPerformance": "Campaign Performance",
  "widgets.cancelled": "Cancelado",
  "widgets.cardGroup": "Card Group",
  "widgets.cardLink": "Card Link",
  "widgets.cardOutline": "Card Outline",
  "widgets.cardSubtitle": "card Subtitle",
  "widgets.cardTitle": "Card Title",
  "widgets.category": "Categoria",
  "widgets.centeredLabels": "Centered Labels",
  "widgets.centeredTabs": "Centered Tabs",
  "widgets.change": "Change",
  "widgets.changeTransition": "Change Transition",
  "widgets.checkboxListControl": "Checkbox List Control",
  "widgets.checklist": "Checklist",
  "widgets.checklistQuestionary": "Checklist Questionário",
  "widgets.chipArray": "Chip Array",
  "widgets.chipWithAvatar": "Chip With Avatar",
  "widgets.chipWithClickEvent": "Chip With Click Event",
  "widgets.chipWithIconAvatar": "Chip With Icon Avatar",
  "widgets.chipWithTextAvatar": "Chip With Text Avatar",
  "widgets.circularProgressBottomStart": "Circular Progress Bottom Start",
  "widgets.code": "Código",
  "widgets.color": "Color",
  "widgets.commments": "Comentários",
  "widgets.company": "Empresa",
  "widgets.companyName": "Empresa",
  "widgets.comparePlans": "Compare nossos planos",
  "widgets.components": "Componentes",
  "widgets.componet": "Components",
  "widgets.ComposeEmail": "Escrever Email",
  "widgets.composeMail": "Novo Email",
  "widgets.conference": "Conference",
  "widgets.confirmationDialogs": "Confirmation Dialogs",
  "widgets.confirmed": "Confirmado",
  "widgets.contextualColoredTable": "Contexual Colored Table",
  "widgets.contexualAlerts": "Contexual Alerts",
  "widgets.contexualColoredSnackbars": "Contexual Colored Snackbars",
  "widgets.contexualColoredTable": "Contexual Colored Table",
  "widgets.contexualVariations": "Contexual Variations",
  "widgets.controlledAccordion": "Controlled Accordion",
  "widgets.croppedImage": "Cropped Image",
  "widgets.culturesCalendar": "Cultures Calendar",
  "widgets.culturesCalender": "Cultures Calender",
  "widgets.currentDate": "Data Atual",
  "widgets.currentTime": "Horário Atual",
  "widgets.customCalender": "Calendário Customizado",
  "widgets.customClickableChip": "Custom Clickable Chip",
  "widgets.customColor": "Custom Color",
  "widgets.customColorCheckbox": "Custom Color Checkbox",
  "widgets.customControlBar": "Custom Control Bar",
  "widgets.customDeleteIconChip": "Custom Delete Icon Chip",
  "widgets.customIconAlert": "Custom Icon Alert",
  "widgets.customPicker": "Custom Picker",
  "widgets.customRendering": "Custom Rendering",
  "widgets.customStyleAlert": "Custom Style Alert",
  "widgets.daily": "Diária",
  "widgets.dailySales": "Daily Sales",
  "widgets.dataTable": "Data Table",
  "widgets.dataUse": "Data Use",
  "widgets.date": "Data",
  "widgets.dateAndTimePicker": "Date And Time Picker",
  "widgets.dateCreated": "Data de Criação",
  "widgets.dateModified": "Data de Modificação",
  "widgets.dates": "Datas",
  "widgets.days_28": "28 dias",
  "widgets.deadline": "Deadline",
  "widgets.defaultDatePicker": "Default Date Picker",
  "widgets.defaultPicker": "Default Picker",
  "widgets.defualtReactForm": "Defualt React Form",
  "widgets.deletableChip": "Deletable Chip",
  "widgets.deleted": "Eliminado",
  "widgets.description": "Descrição",
  "widgets.descriptionAlert": "Description Alert",
  "widgets.designation": "Designation",
  "widgets.determinate": "Determinate",
  "widgets.dialogs": "Dialogs",
  "widgets.disableChip": "Disable Chip",
  "widgets.disabledCheckbox": "Disabled Checkbox",
  "widgets.disabledRadio": "Disabled Radio",
  "widgets.discoverPeople": "Discover People",
  "widgets.done": "Finalizado",
  "widgets.doughnut": "Doughnut",
  "widgets.downshiftAutoComplete": "Downshift Auto Complete",
  "widgets.dragAndDropCalendar": "Drag And Drop Calendar",
  "widgets.dragAndDropCalender": "Drag And Drop Calender",
  "widgets.dragula": "Dragula",
  "widgets.emailsStatistics": "Estatísticas de Emails",
  "widgets.employeeList": "Employee List",
  "widgets.endDate": "Fim",
  "widgets.enterpriseEdition": "Enterprise Edition",
  "widgets.enterYourPassword": "Insira sua Senha",
  "widgets.expenseCategory": "Expense Category",
  "widgets.expenses": "Expenses",
  "widgets.exportToExcel": "Exportar para Excel",
  "widgets.externalUrl": "URL Externa",
  "widgets.faq(s)": "Faq(s)",
  "widgets.file": "Arquivo",
  "widgets.filters": "Filtros",
  "widgets.fixedTabs": "Fixed Tabs",
  "widgets.flatButtons": "Flat Buttons",
  "widgets.floatingActionButtons": "Floating Action Buttons",
  "widgets.folderLists": "Folder Lists",
  "widgets.follow": "seguir",
  "widgets.follower": "Follower",
  "widgets.following": "Seguindo",
  "widgets.forcedScrolledButtons": "Forced Scrolled Buttons",
  "widgets.forgetPassword": "Forget Password",
  "widgets.formattedInputs": "Formatted Inputs",
  "widgets.formDialogs": "Form Dialogs",
  "widgets.formGrid": "Form Grid",
  "widgets.formValidate": "Form Validate",
  "widgets.formValidation": "Form Validation",
  "widgets.free": "Free",
  "widgets.frequentlyAskedQuestions": "Frequently Asked Questions",
  "widgets.frontend": "Frontend",
  "widgets.fullDescription": "Descrição Completa",
  "widgets.fullScreenDialogs": "Full Screen Dialogs",
  "widgets.gallery": "Galeria",
  "widgets.global": "Global",
  "widgets.helpButtonTooltip": "Ajuda",
  "widgets.helpToShareText":
    "Help us spread the world by sharing our website with your friends and followers on social media!",
  "widgets.hiddenLabels": "Hidden Labels",
  "widgets.high": "High",
  "widgets.horizontalBar": "Horizontal Bar",
  "widgets.horizontalLinear": "Horizontal Linear",
  "widgets.horizontalLinearAlternativeLabel":
    "Horizontal Linear Alternative Label",
  "widgets.horizontalLinerAlternativeLabel":
    "Horizontal Liner Alternative Label",
  "widgets.horizontalNonLinear": "Horizontal Non Linear",
  "widgets.horizontalNonLinearAlternativeLabel":
    "Horizontal Non Linear Alternative Label",
  "widgets.horizontalNonLinerAlternativeLabel":
    "Horizontal Non Liner Alternative Label",
  "widgets.horizontalStyleCheckbox": "Horizontal Style Checkbox",
  "widgets.howWouldYouRateUs": "How would you rate us?",
  "widgets.httpLiveStreaming": "HTTP Live Streaming",
  "widgets.iconButton": "Icon Button",
  "widgets.iconNavigation": "Icon Navigation",
  "widgets.iconsAvatars": "Icons Avatars",
  "widgets.iconsTabs": "Icons Tabs",
  "widgets.iconWithLabel": "Icon With Label",
  "widgets.imageAvatars": "Image Avatars",
  "widgets.imageOnlyGridLists": "Image Only Grid Lists",
  "widgets.important": "Important",
  "widgets.indeterminate": "Indeterminate",
  "widgets.inlineForm": "Inline Form",
  "widgets.inputGridSizing": "Input Grid Sizing",
  "widgets.inputSizing": "Input Sizing",
  "widgets.inputWithDanger": "Input With Danger",
  "widgets.inputWithSuccess": "Input With Success",
  "widgets.insetDividers": "Inset Dividers",
  "widgets.insetLists": "Inset Lists",
  "widgets.interactiveIntegration": "Interactive Integration",
  "widgets.InteractiveLists": "Interactive Lists",
  "widgets.interminateSelection": "Interminate Selection",
  "widgets.issue": "Issue",
  "widgets.keyboardShortcuts": "Atalhos de Teclado",
  "widgets.lastMonth": "Last Month",
  "widgets.lastWeek": "Semana passada",
  "widgets.latestPost": "Latest Post",
  "widgets.layouts": "Layouts",
  "widgets.lettersAvatars": "Letters Avatars",
  "widgets.lifetime": "Vitalício",
  "widgets.linearProgressLineBar": "Linear Progress Line Bar",
  "widgets.lineBarAreaChart": "Line Bar Area Chart",
  "widgets.lineChart": "Line Chart",
  "widgets.listDividers": "List Dividers",
  "widgets.listing": "Listando",
  "widgets.listItemWithImage": "List Item With Image",
  "widgets.LiveChatSupport": "Suporte Live Chat",
  "widgets.lockScreen": "Lock Screen",
  "widgets.logIn": "Log In",
  "widgets.logOut": "Sair",
  "widgets.logs": "Logs",
  "widgets.low": "Low",
  "widgets.mail": "Email",
  "widgets.mailing.grapesjs.copy":
    'Variavel "[%val]" copiado para a área de transferência. Use "CTRL+V" para colar',
  "widgets.master": "Master",
  "widgets.materialBadge": "Material Badge",
  "widgets.maxHeightMenu": "Max Height Menu",
  "widgets.Mega": "Mega",
  "widgets.message": "Mensagem",
  "widgets.messages": "Mensagens",
  "widgets.ModulesPostsSelector": "Selecione",
  "widgets.monthly": "Mensal",
  "widgets.multilevel": "Multilevel",
  "widgets.multipleTabs": "Multiple Tabs",
  "widgets.multiSelectList": "Multi Select List",
  "widgets.MutltiSelectList": "Mutlti Select List",
  "widgets.myProfile": "Meu Perfil",
  "widgets.nativeSelect": "Native Select",
  "widgets.nestedLists": "Nested Lists",
  "widgets.netProfit": "Net Profit",
  "widgets.new": "Novo",
  "widgets.new.fem": "Nova",
  "widgets.new.plural": "Novos",
  "widgets.new.plural.fem": "Novas",
  "widgets.newCustomers": "Novos Clientes",
  "widgets.newEmails": "Novos Emails",
  "widgets.noLogsYet": "Não há logs por enquanto",
  "widgets.note": "Note",
  "widgets.notifications": "Notificações",
  "widgets.number": "Number",
  "widgets.occupation": "Profissão",
  "widgets.OngoingProjects": "Projetos em Andamento",
  "widgets.onlineSources": "Online Sources",
  "widgets.onlineVistors": "Visitantes",
  "widgets.open": "Aberto",
  "widgets.openAlertDialog": "Open Alert Dialog",
  "widgets.openBottom": "Open Bottom",
  "widgets.openFormDialog": "Open Form Dialog",
  "widgets.openLeft": "Open Left",
  "widgets.openResponsiveDialog": "Open Responsive Dialog",
  "widgets.openRight": "Open Right",
  "widgets.openSimpleDialog": "Open Simple Dialog",
  "widgets.openTop": "Open Top",
  "widgets.optionA": "Option A",
  "widgets.optionB": "Option B",
  "widgets.optionC": "Option C",
  "widgets.optionM": "Option M",
  "widgets.optionN": "Option N",
  "widgets.optionO": "Option O",
  "widgets.orderDate": "Data do Pedido",
  "widgets.orders": "Pedidos",
  "widgets.orderStatus": "Status do Pedido",
  "widgets.ourLocations": "Our Locations",
  "widgets.ourMissions": "Our Missions",
  "widgets.ourMotivation": "Our Motivation",
  "widgets.ourVission": "Our Vission",
  "widgets.outlineChip": "Outline Chip",
  "widgets.overallTrafficStatus": "Tráfego",
  "widgets.overlayCard": "Overlay Card",
  "widgets.paid": "Pago",
  "widgets.paper": "Paper",
  "widgets.password": "Senha",
  "widgets.passwordPromptAlert": "Password Prompt Alert",
  "widgets.pending": "Pendente",
  "widgets.permanentdrawer": "Permanent Drawer",
  "widgets.permanentDrawers": "Permanent Drawers",
  "widgets.persistentdrawer": "Persistent Drawer",
  "widgets.personalDetails": "Personal Details",
  "widgets.personalEdition": "Personal Edition",
  "widgets.personalSchedule": "Minha Agenda",
  "widgets.phoneNo": "Telefone",
  "widgets.pieChart": "Pie Chart",
  "widgets.pinedSubHeader": "Pined Sub Header",
  "widgets.pinnedSubheaderList": "Pinned Subheader List",
  "widgets.pixelScript": "Script do Pixel QIPlus",
  "widgets.plan": "Plano",
  "widgets.plans": "Planos",
  "widgets.polarChart": "Polar Chart",
  "widgets.positionedSnackbar": "Positioned Snackbar",
  "widgets.positionedToolTips": "Positioned Snackbar",
  "widgets.PostsSelector": "Selecione",
  "widgets.preventScrolledButtons": "Prevent Scrolled Buttons",
  "widgets.preview": "Pré-visualização",
  "widgets.previousChat": "Previous Chat",
  "widgets.price": "Preço",
  "widgets.pricing": "Preços",
  "widgets.primary": "Primary",
  "widgets.private": "Private",
  "widgets.pro": "Pro",
  "widgets.productReports": "Relatórios de Produtos",
  "widgets.productsReports": "Products Reports",
  "widgets.productStats": "Estatísticas de Produtos",
  "widgets.professional": "Profissional",
  "widgets.professionals": "Profissionais",
  "widgets.profile": "Perfil",
  "widgets.projectManagement": "Project Management",
  "widgets.ProjectStatus": "Status do Projeto",
  "widgets.projectTaskManagement": "Project Task Management",
  "widgets.promptAlert": "Prompt Alert",
  "widgets.qiplusPlan": "Plano QIPlus",
  "widgets.qiplusUrl": "URL do QIPlus",
  "widgets.query": "Query",
  "widgets.QuickLinks": "Quick Links",
  "widgets.quillEditor": "Quill Editor",
  "widgets.quoteOfTheDay": "Quote Of The Day",
  "widgets.radarChart": "Radar Chart",
  "widgets.radioButtons": "Radio Buttons",
  "widgets.raisedButton": "Raised Button",
  "widgets.ratings": "Ratings",
  "widgets.reactAutoSuggest": "React Auto Suggest",
  "widgets.reactAutoSuggests": "React Auto Suggests",
  "widgets.reactButton": "React Button",
  "widgets.reactDND": "React DND",
  "widgets.reactGridControlledStateMode": "React Grid Controlled State Mode",
  "widgets.reactSelect": "React Select",
  "widgets.recentActivities": "Atividade Recente",
  "widgets.recentChat": "Recent Chat",
  "widgets.recentNotifications": "Notificações Recentes",
  "widgets.recentOrders": "Últimos Pedidos",
  "widgets.RecentOrders": "Últimos Pedidos",
  "widgets.recents": "Recents",
  "widgets.refunded": "Reembolsado",
  "widgets.reject": "Descartar",
  "widgets.responsiveFlipTable": "Responsive Flip Table",
  "widgets.responsiveFullScreen": "Responsive Full Screen",
  "widgets.responsiveTable": "Responsive Table",
  "widgets.sales": "Vendas",
  "widgets.saveAsDrafts": "Save As Drafts",
  "widgets.search": "Pesquisar",
  "widgets.searchIdeas": "Pesquisar Ideas",
  "widgets.searchInMailbox": "Pesquisar em Emails",
  "widgets.searchMailList": "Pesquisar nos emails",
  "widgets.secondaryHeadingAndColumns": "Secondary Heading And Columns",
  "widgets.selectableCalender": "Selectable Calender",
  "widgets.selectADefaultAddress": "Select A Default Address",
  "widgets.selectedMenu": "Selected Menu",
  "widgets.selectMultiple": "Select Multiple",
  "widgets.selectProject": "Select Project",
  "widgets.selectTripDestination": "Select Trip Destination",
  "widgets.send": "Enviar",
  "widgets.ShareWithFriends": "Compartilhe!",
  "widgets.shipTo": "Enviar a",
  "widgets.shortDescription": "Descrição Breve",
  "widgets.signIn": "Acessar",
  "widgets.signUp": "Registro",
  "widgets.Simple App Bars": "Simple App Bars",
  "widgets.simpleAppBar": "Simple App Bar",
  "widgets.simpleCards": "Simple Cards",
  "widgets.simpleCheckbox": "Simple Checkbox",
  "widgets.simpleDialogs": "Simple Dialogs",
  "widgets.simpleExpansionPanel": "Simple Expansion Panel",
  "widgets.simpleLists": "Simple Lists",
  "widgets.simpleMenus": "Simple Menus",
  "widgets.simpleSelect": "Simple Select",
  "widgets.simpleSnackbar": "Simple Snackbar",
  "widgets.simpleTextField": "Simple Text Field",
  "widgets.singleLineGridLists": "Single Line Grid Lists",
  "widgets.singleLineItem": "Single Line Item",
  "widgets.siteVisitors": "Visitantes",
  "widgets.social": "Social",
  "widgets.socialCompanines": "Social Companines",
  "widgets.socialMediaButton": "Social Media Button",
  "widgets.socialNewtork": "Rede Social",
  "widgets.socialNewtorks": "Redes Sociais",
  "widgets.spam": "Spam",
  "widgets.speacialTitleTreatment": "Speacial Title Treatment",
  "widgets.stackedAreaChart": "Stacked Area Chart",
  "widgets.stackedBarChart": "Stacked Bar Chart",
  "widgets.standard": "Standard",
  "widgets.starred": "Starred",
  "widgets.startDate": "Início",
  "widgets.startToBasic": "Começar com Básico",
  "widgets.status": "Status",
  "widgets.stepper": "Stepper",
  "widgets.stockExchange": "Stock Exchange",
  "widgets.styles": "Estilos",
  "widgets.subject": "Assunto",
  "widgets.successAlert": "Operação realizada com successo",
  "widgets.support": "Suporte",
  "widgets.supportRequest": "Support Request",
  "widgets.swiches": "Swiches",
  "widgets.switches": "Swiches",
  "widgets.switchLists": "Switch Lists",
  "widgets.tabs": "Tabs",
  "widgets.target": "Target",
  "widgets.taskList": "Tarefas",
  "widgets.tax": "Tax",
  "widgets.team": "Equipe",
  "widgets.teamEdition": "Team Edition",
  "widgets.temporaryDrawers": "Temporary Drawers",
  "widgets.text": "Texto",
  "widgets.textArea": "Text Area",
  "widgets.thisWeek": "Esta semana",
  "widgets.time": "Horário",
  "widgets.timePicker": "Time Picker",
  "widgets.to": "Para",
  "widgets.today": "Hoje",
  "widgets.todayOrders": "Pedidos do Dia",
  "widgets.toDoList": "Tarefas",
  "widgets.tooltip": "ToolTip",
  "widgets.topSellings": "Top Sellings",
  "widgets.total": "Total",
  "widgets.total_over_range": "Total Acima da Faixa",
  "widgets.totalActiveUsers": "Total de usuários ativos",
  "widgets.totalOrders": "Total de Pedidos",
  "widgets.totalRequest": "Total Request",
  "widgets.totalRevenue": "Total Revenue",
  "widgets.totalSales": "Total de Vendas",
  "widgets.totalVisitors": "Total de Visitantes",
  "widgets.trackingNumber": "Tracking Number",
  "widgets.trafficChannel": "Traffic Channel",
  "widgets.trafficSources": "Traffic Sources",
  "widgets.transactionList": "Transaction List",
  "widgets.transferReport": " Transfer Report",
  "widgets.transitionControlDirection": "Transition Control Direction",
  "widgets.TriggersBuilder": "Selecione os disparadores",
  "widgets.tutorials": "Tutoriais",
  "widgets.tweets": "Tweets",
  "widgets.typeYourQuestions": "Type Your Questions",
  "widgets.uncontrolledDisableAlerts": "Uncontrolled Disable Alerts",
  "widgets.unitPrice": "Preço unitário",
  "widgets.unset": "Não definido",
  "widgets.unsubscribe": "Unsubscribe",
  "widgets.upcomingEvents": "Próximos eventos",
  "widgets.updated10Minago": "Atualizado há 10 min",
  "widgets.updateProfile": "Atualizar Perfil",
  "widgets.updateYourEmailAddress": "Update Your Email Address",
  "widgets.upgrade": "upgrade",
  "widgets.upgradePlan": "Alterar meu Plano",
  "widgets.upgradeToAdvance": "Upgrade To Advance",
  "widgets.upgradeToEnableAction": "Faça upgrade para habilitar esta ação",
  "widgets.upgradeToPro": "Upgrade To Pro",
  "widgets.url": "URL",
  "widgets.user": "Usuário",
  "widgets.username": "Usuário",
  "widgets.usersList": "Lista de Usuários ",
  "widgets.verticalChart": "Vertical Chart",
  "widgets.verticalStepper": "Vertical Stepper",
  "widgets.VerticalStyleCheckbox": "Vertical Style Checkbox",
  "widgets.visitors": "Visitantes",
  "widgets.volume": "Volume",
  "widgets.warningAlert": "Warning Alert",
  "widgets.weekly": "Semanal",
  "widgets.weekPicker": "Week Picker",
  "widgets.widgets": "Widgets",
  "widgets.withDisableTabs": "With Disable Tabs",
  "widgets.withDownloadButton": "With Download Button",
  "widgets.withError": "With Error",
  "widgets.withHtmlAlert": "With Html Alert",
  "widgets.wordpressTheme": "Wordpress Theme",
  "widgets.workWeek": "Work Week",
  "widgets.wrappedLabels": "Wrapped Labels",
  "widgets.yearly": "Anual",
  "widgets.yesterday": "Ontem",

  // WhatsApp Groups Management
  "whatsapp.groups.manager.title": "Gestão de Grupos WhatsApp",
  "whatsapp.groups.manager.subtitle": "Organize seus leads em grupos WhatsApp para envio de ofertas e comunicação em massa",
  "whatsapp.groups.step.selectInstance": "Selecionar Instância WhatsApp",
  "whatsapp.groups.step.selectLeads": "Selecionar Leads",
  "whatsapp.groups.step.configureGroups": "Configurar Grupos",
  "whatsapp.groups.step.createGroups": "Criar Grupos",
  "whatsapp.groups.selectInstance.description": "Selecione a instância WhatsApp que será usada para criar os grupos:",
  "whatsapp.groups.selectInstance.label": "Selecionar Instância",
  "whatsapp.groups.selectInstance.loading": "Carregando...",
  "whatsapp.groups.selectInstance.connected": "Conectado",
  "whatsapp.groups.selectInstance.disconnected": "Desconectado",
  "whatsapp.groups.selectLeads.description": "Selecione os leads que serão adicionados aos grupos WhatsApp:",
  "whatsapp.groups.selectLeads.available": "Total de leads disponíveis:",
  "whatsapp.groups.selectLeads.selected": "Selecionados:",
  "whatsapp.groups.selectLeads.title": "Seleção de Leads",
  "whatsapp.groups.selectLeads.selectAll": "Selecionar Todos",
  "whatsapp.groups.selectLeads.clearSelection": "Limpar Seleção",
  "whatsapp.groups.selectLeads.selectFirst100": "Primeiros 100",
  "whatsapp.groups.selectLeads.selectFirst500": "Primeiros 500",
  "whatsapp.groups.selectLeads.percentSelected": "selecionado",
  "whatsapp.groups.configureGroups.description": "Configure os grupos que serão criados:",
  "whatsapp.groups.configureGroups.prefix.label": "Prefixo dos Grupos",
  "whatsapp.groups.configureGroups.prefix.placeholder": "Ex: Ofertas Produto",
  "whatsapp.groups.configureGroups.prefix.helper": "Os grupos serão nomeados como: [Prefixo] 1, [Prefixo] 2, etc.",
  "whatsapp.groups.configureGroups.summary.title": "Resumo da Criação",
  "whatsapp.groups.configureGroups.summary.leadsSelected": "Leads selecionados:",
  "whatsapp.groups.configureGroups.summary.groupsToCreate": "Grupos a serem criados:",
  "whatsapp.groups.configureGroups.summary.maxLeadsPerGroup": "Máximo de leads por grupo:",
  "whatsapp.groups.configureGroups.summary.instance": "Instância WhatsApp:",
  "whatsapp.groups.createGroups.description": "Criando grupos WhatsApp...",
  "whatsapp.groups.createGroups.progress": "Progresso:",
  "whatsapp.groups.createGroups.created.title": "Grupos Criados",
  "whatsapp.groups.createGroups.leads": "leads",
  "whatsapp.groups.createGroups.creating": "Criando...",
  "whatsapp.groups.createGroups.create": "Criar Grupos",
  "whatsapp.groups.createGroups.restart": "Reiniciar",
  "whatsapp.groups.completed.title": "Processo Concluído!",
  "whatsapp.groups.completed.description": "Os grupos WhatsApp foram criados com sucesso. Você pode visualizar e gerenciar os grupos criados na seção de grupos.",
  "whatsapp.groups.completed.createNew": "Criar Novos Grupos",
  "whatsapp.groups.error.loadInstances": "Erro ao carregar instâncias WhatsApp",
  "whatsapp.groups.error.loadLeads": "Erro ao carregar leads",
  "whatsapp.groups.error.createGroups": "Erro ao criar grupos",
  "whatsapp.groups.error.noGroupsCreated": "Nenhum grupo foi criado com sucesso.",
  "whatsapp.groups.success.groupsCreated": "grupos criados com sucesso!",
  "whatsapp.groups.button.back": "Voltar",
  "whatsapp.groups.button.next": "Próximo",

  // Menu ShotX
  "shotx.groups.menu.title": "Grupos",
};


const pendingTranslationStrings = {
  'pending.translations': 'Should be here',
}

const moduleStrings = {}

Object.keys(AppModules).forEach(collection => {
  const thisModule = AppModules[collection]
  const menuGroup = thisModule.menu_group
  const collectionKeys = ['modules', 'sidebar', 'menu', 'triggers']

  let { label, singular, menu_label, gender, shortname } = thisModule
  const menulabel = menu_label || label

  collectionKeys.forEach(key => {
    const thisLabel = key === 'menu' ? menulabel : label
    const singularLabel = singular || thisLabel
    const shortLabel = shortname || singularLabel

    moduleStrings[`${key}.${collection}`] = thisLabel
    moduleStrings[`${key}.${collection}.singular`] = singularLabel
    moduleStrings[`${key}.${collection}.short`] = shortLabel

    moduleStrings[`${key}.${collection}.added`] = `${singularLabel} Criad[%gender]`.replace('[%gender]', gender === 'f' ? 'a' : 'o')
    moduleStrings[`${key}.${collection}.changed`] = `${singularLabel} Modificad[%gender]`.replace('[%gender]', gender === 'f' ? 'a' : 'o')
    moduleStrings[`${key}.${collection}.removed`] = `${singularLabel} Removid[%gender]`.replace('[%gender]', gender === 'f' ? 'a' : 'o')

    moduleStrings[`${key}.${collection}_added`] = moduleStrings[`${key}.${collection}.added`]
    moduleStrings[`${key}.${collection}_changed`] = moduleStrings[`${key}.${collection}.changed`]
    moduleStrings[`${key}.${collection}_removed`] = moduleStrings[`${key}.${collection}.removed`]

    if (menuGroup) {
      moduleStrings[`${key}.${menuGroup}Group`] = thisLabel

      if (!moduleStrings[`${key}.${menuGroup}`]) {
        moduleStrings[`${key}.${menuGroup}`] = thisLabel
        moduleStrings[`${key}.${menuGroup}.singular`] = singularLabel
        moduleStrings[`${key}.${menuGroup}.short`] = shortLabel
      }
    }
  })

  const editKeys = {
    view: 'Ver [%collection]',
    'view.singular': 'Ver [%singular]',
    new: 'Nov[%gender] [%singular]',
    create: 'Criar [%singular]',
    add: 'Adicionar [%singular]',
    createNew: 'Criar nov[%gender] [%singular]',
    update: 'Atualizar [%singular]',
    edit: 'Editar [%singular]',
    delete: 'Eliminar [%singular]',
    remove: 'Excluir [%singular]',
    'clone.singular': 'Clonar [%singular]',
    clone: 'Clonar [%collection]',
    model: 'Modelo de [%singular]',
    example: 'Exemplo de [%singular]',
    choose: 'Selecione um[%fem] [%singular]',
    select: 'Selecionar [%collection]',
    selected: '[%collection] selecionad[%gender]s',
    'selected.singular': '[%singular] selecionad[%gender]',
    import: 'Importar [%collection]',
    associate: 'Associar [%collection]',
    data: 'Dados d[%gender] [%singular]',
    confirmRemoval: 'Deseja eliminar [%gender] [%singular]?',
    confirmUpdate: 'Deseja atualizar [%gender] [%singular]?',
    willBeSentToTrash: '[%gender] [%singular] será enviad[%gender] para a lixeira',
    'willBeSentToTrash.plural': '[%gender] [%collection] serão enviad[%gender]s para a lixeira',
    willBeRemoved: '[%gender] [%singular] será eliminad[%gender]',
    'willBeRemoved.plural': '[%gender]s [%collection] serão eliminad[%gender]s',
    sentToTrash: '[%singular] enviad[%gender] para a lixeira',
    'sentToTrash.plural': '[%collection] enviad[%gender]s para a lixeira',
    removed: '[%gender] [%singular] será eliminad[%gender]',
    'removed.plural': '[%gender]s [%collection] serão eliminad[%gender]s',
    savedMsg: '[%singular] salv[%gender] com sucesso',
    deletedMsg: '[%singular] eliminad[%gender]',
    'deletedMsg.plural': '[%collection] eliminad[%gender]s',
    'deletedMsg.failure': '[%collection] não puderam ser eliminad[%gender]s',
    createdMsg: '[%singular] criad[%gender] com sucesso',
    'createdMsg.plural': '[%collection] criad[%gender]s com sucesso',
    'createdMsg.failure': '[%collection] não puderam ser criad[%gender]s',
    updatedMsg: '[%singular] atualizad[%gender]',
    'updatedMsg.plural': '[%collection] atualizad[%gender]s',
    'updatedMsg.failure': '[%collection] não puderam ser atualizad[%gender]s',
    inexistsMsg: '[%gender] [%singular] não existe no banco de dados',
    noPostsFound: 'Nenhum[%fem] [%singular] encontrad[%gender]',
    savePostBefore: 'Salve [%gender] [%singular] antes de continuar',
    loginForReport: 'Efetue o login para visualizar os relatórios',
    leaveBlankForUnlimited: 'Deixe em branco para disponibilizar tod[%gender]s [%gender]s [%collection] da conta',
  }

  Object.keys(editKeys).forEach(key => {
    let msg = editKeys[key]

    const fem = gender === 'f' ? 'a' : ''
    const masc = gender === 'm' ? 'o' : ''
    const genderVocal = gender === 'f' ? 'a' : 'o'
    const singularLabel = singular || label
    const shortLabel = shortname || singularLabel

    msg = msg.replace(/\[%collection\]/g, label)
    msg = msg.replace(/\[%singular\]/g, singularLabel)
    msg = msg.replace(/\[%shortname\]/g, shortLabel)
    msg = msg.replace(/\[%gender\]/g, genderVocal)
    msg = msg.replace(/\[%fem\]/g, fem)
    msg = msg.replace(/\[%masc\]/g, masc)

    moduleStrings[`${collection}.${key}`] = msg
    moduleStrings[`${collection}.${key}.short`] = msg.replace(singularLabel, shortLabel)
  })
})

Object.keys(AppTaxonomies).forEach(taxName => {
  const thisTaxonomy = AppTaxonomies[taxName]
  const menuGroup = thisTaxonomy.menu_group
  const collectionKeys = ['taxonomies', 'sidebar', 'menu']

  let { label, singular, menu_label, gender } = thisTaxonomy
  const menulabel = menu_label || label

  const editKeys = {
    view: 'Ver [%taxName]',
    'view.singular': 'Ver [%singular]',
    new: 'Nov[%gender] [%singular]',
    create: 'Criar [%singular]',
    add: 'Adicionar [%singular]',
    createNew: 'Criar nov[%gender] [%singular]',
    update: 'Atualizar [%singular]',
    edit: 'Editar [%singular]',
    delete: 'Eliminar [%singular]',
    remove: 'Excluir [%singular]',
    'clone.singular': 'Clonar [%singular]',
    clone: 'Clonar [%taxName]',
    model: 'Modelo de [%singular]',
    example: 'Exemplo de [%singular]',
    choose: 'Selecione um[%fem] [%taxName]',
    select: 'Selecionar [%taxName]',
    associate: 'Associar [%taxName]',
    data: 'Dados d[%gender] [%singular]',
    confirmRemoval: 'Deseja eliminar [%gender] [%singular]?',
    confirmUpdate: 'Deseja atualizar [%gender] [%singular]?',
    willBeSentToTrash: '[%gender] [%singular] será enviad[%gender] para a lixeira',
    willBeRemoved: '[%gender] [%singular] será eliminad[%gender]',
    deletedMsg: '[%singular] eliminad[%gender]',
    'deletedMsg.plural': '[%collection] eliminad[%gender]s',
    'deletedMsg.failure': '[%collection] não puderam ser eliminad[%gender]s',
    createdMsg: '[%singular] criad[%gender] com sucesso',
    'createdMsg.plural': '[%collection] criad[%gender]s com sucesso',
    'createdMsg.failure': '[%collection] não puderam ser criad[%gender]s',
    updatedMsg: '[%singular] atualizad[%gender]',
    'updatedMsg.plural': '[%collection] atualizad[%gender]s',
    'updatedMsg.failure': '[%collection] não puderam ser atualizad[%gender]s',
    inexistsMsg: '[%gender] [%singular] não existe no banco de dados',
    noPostsFound: 'Nenhum[%fem] [%singular] encontrad[%gender]',
  }

  thisTaxonomy.collections.forEach(collection => {
    const thisModule = AppModules[collection]

    collectionKeys.forEach(key => {
      const thisLabel = key === 'menu' ? menulabel : label
      const singularLabel = singular || thisLabel

      moduleStrings[`${key}.${taxName}.${collection}`] = thisLabel
      moduleStrings[`${key}.${taxName}.${collection}.singular`] = singularLabel

      if (menuGroup) {
        moduleStrings[`${key}.${menuGroup}Group`] = thisLabel

        if (!moduleStrings[`${key}.${menuGroup}`]) {
          moduleStrings[`${key}.${menuGroup}`] = thisLabel
          moduleStrings[`${key}.${menuGroup}.singular`] = singularLabel
        }
      }
    })

    Object.keys(editKeys).forEach(key => {
      let msg = editKeys[key]

      const fem = gender === 'f' ? 'a' : ''
      const masc = gender === 'm' ? 'o' : ''
      const genderVocal = gender === 'f' ? 'a' : 'o'
      const singularLabel = singular || label

      msg = msg.replace(/\[%taxName\]/g, label)
      msg = msg.replace(/\[%collection\]/g, thisModule.label)
      msg = msg.replace(/\[%singular\]/g, singularLabel)
      msg = msg.replace(/\[%gender\]/g, genderVocal)
      msg = msg.replace(/\[%fem\]/g, fem)
      msg = msg.replace(/\[%masc\]/g, masc)

      moduleStrings[`${taxName}.${collection}.${key}`] = msg

      if (!moduleStrings[`${taxName}.${key}`]) {
        moduleStrings[`${taxName}.${key}`] = msg
      }
    })
  })
})

const langMessages = {
  ...moduleStrings,
  ...pendingTranslationStrings,
  ...pt_BR_Strings,
}

module.exports = langMessages
