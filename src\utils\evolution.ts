import { Config } from '../constants/config'
import { MessageUpSert, MessageUpSertData } from '../libs/types/evolution'

const separator = Config.instanceNameSeparator
export const makeInstanceName = (accountId: string, id: string) => {
  return `${accountId}${separator}${id}`
}

export const getInstanceId = (instance: any) => {
  return instance.split(separator)[1]
}

export const getInstanceAccountId = (instance: any) => {
  return instance.split(separator)[0]
}

const isBrazilianPhone = (phone: string) => {
  const phoneLengths = [11, 12, 13]
  return (
    (phone.startsWith('55') || phone.startsWith('+55')) &&
    phoneLengths.includes(phone.length)
  )
}

const getCCFromPhone = (phone: string) => {
  let countryCode = ''
  let locale = 'en'

  if (isBrazilianPhone(phone)) {
    countryCode = '55'
    locale = 'pt_BR'
  }

  return { countryCode, locale }
}

export const getPhoneFromRemoteJid = (remoteJid: string) => {
  const phone = remoteJid.split('@')[0]
  const { countryCode, locale } = getCCFromPhone(phone)
  const phoneOnly = phone.slice(countryCode.length)
  return {
    phone,
    countryCode,
    phoneOnly,
    locale,
  }
}

export const getPhoneFromContactId = (contactId: string) => {
  return getPhoneFromRemoteJid(`${contactId}@contact.id`)
}

export const sanitizeUpSertEventData = (
  data: MessageUpSert
): MessageUpSertData => {
  const {
    data: { key, pushName, source },
    instance,
  } = data

  const { phoneOnly, countryCode, locale, phone } = getPhoneFromRemoteJid(
    key.remoteJid
  )

  const firstName = pushName?.split(' ')[0] || ''
  const lastName = pushName?.split(' ')[1] || ''
  const pushNameAlt = pushName || phone // Caso o nome venha vazio utiliza o numero de telefone
  const instanceId = getInstanceId(instance)
  const accountId = getInstanceAccountId(instance)

  return {
    ...data.data,
    pushName: pushNameAlt,
    firstName,
    lastName,
    phone: phoneOnly,
    accountId,
    countryCode,
    instanceId,
    source,
    locale,
  }
}
