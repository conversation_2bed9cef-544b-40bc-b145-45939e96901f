import {
  Box,
  TextField,
  Typography
} from '@material-ui/core';
import { makeStyles } from '@material-ui/core/styles';
import { Autocomplete } from '@material-ui/lab';
import { langMessages } from 'Lang/index';
import React, { useState } from 'react';

const useStyles = makeStyles((theme) => ({
  root: {
    marginBottom: theme.spacing(2),
  },
  messagePreview: {
    marginTop: theme.spacing(2),
    padding: theme.spacing(2),
    backgroundColor: theme.palette.grey[50],
    borderRadius: theme.shape.borderRadius,
  },
  option: {
    fontSize: 14,
    '& > span': {
      marginRight: 10,
      fontSize: 18,
    },
  },
}));

const SniperAutocomplete = ({
  snipers,
  selectedSniper,
  onSniperChange,
  disabled = false
}) => {
  const classes = useStyles();
  const [inputValue, setInputValue] = useState('');

  return (
    <div className={classes.root}>
      <Autocomplete
        options={snipers}
        disabled={disabled}
        getOptionLabel={(option) => option.title || option.publicSniper || 'ShotFlow sem nome'}
        value={snipers.find((m) => m.id.toString() === selectedSniper) || null}
        onChange={(event, newValue) => {
          onSniperChange(newValue ? newValue.id.toString() : '');
        }}
        inputValue={inputValue}
        onInputChange={(event, newInputValue) => {
          setInputValue(newInputValue);
        }}
        renderInput={(params) => (
          <TextField
            {...params}
            disabled={disabled}
            label={langMessages['shotx.broadcast.sniperSelectLabel']}
            variant="outlined"
            fullWidth
          />
        )}
        renderOption={(option) => (
          <Box className={classes.option}>
            <Typography variant="body2">{option.title || option.publicSniper || 'Sniper sem nome'}</Typography>
          </Box>
        )}
      />
    </div>
  );
};

export default SniperAutocomplete;
