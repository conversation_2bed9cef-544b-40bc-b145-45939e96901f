# WhatsApp Groups API - QIP-526 Implementation

This document describes the backend API endpoints implemented for the WhatsApp group creation functionality (QIP-526) that supports the frontend requirements detailed in QIP-527.

## 🎯 Overview

The API provides endpoints to create and manage WhatsApp groups with support for:
- Creating multiple groups with up to 500 leads per group
- Handling up to 5000 leads total
- Supporting the 4-step workflow from the frontend
- Mock implementation for development/testing

## 📋 API Endpoints

### 1. Create Single Group

**POST** `/whatsapp/groups/create/:instanceId`

Creates a single WhatsApp group.

**Headers:**
- `Authorization: Bearer <token>` (required)

**Parameters:**
- `instanceId` (path): WhatsApp instance ID

**Body:**
```json
{
  "accountId": "string",
  "userId": "string",
  "groupName": "string",
  "description": "string (optional)",
  "members": ["phone1", "phone2", "..."]
}
```

**Response:**
```json
{
  "error": false,
  "data": {
    "success": true,
    "groupId": "string",
    "groupJid": "string",
    "message": "Group created successfully"
  }
}
```

### 2. Create Multiple Groups (Main Feature)

**POST** `/whatsapp/groups/create-multiple/:instanceId`

Creates multiple WhatsApp groups automatically dividing leads into groups of ~500 members each.

**Headers:**
- `Authorization: Bearer <token>` (required)

**Parameters:**
- `instanceId` (path): WhatsApp instance ID

**Body:**
```json
{
  "accountId": "string",
  "userId": "string",
  "groupPrefix": "string",
  "maxLeadsPerGroup": 500,
  "leads": [
    {
      "id": "string",
      "firstName": "string",
      "lastName": "string (optional)",
      "phone": "string",
      "displayName": "string (optional)"
    }
  ]
}
```

**Response:**
```json
{
  "error": false,
  "data": {
    "success": true,
    "totalGroups": 10,
    "createdGroups": [
      {
        "id": "string",
        "name": "Group Prefix 1",
        "description": "string",
        "members": [...],
        "createdAt": "2024-01-01T00:00:00.000Z",
        "instanceId": "string",
        "accountId": "string",
        "groupJid": "string",
        "status": "created"
      }
    ],
    "failedGroups": [
      {
        "name": "Group Prefix 2",
        "error": "Error message",
        "members": [...]
      }
    ],
    "message": "Created 9 out of 10 groups"
  }
}
```

### 3. List Groups

**GET** `/whatsapp/groups/list/:instanceId?accountId=string`

Lists all groups for a WhatsApp instance.

**Headers:**
- `Authorization: Bearer <token>` (required)

**Parameters:**
- `instanceId` (path): WhatsApp instance ID
- `accountId` (query): Account ID

**Response:**
```json
{
  "error": false,
  "data": [
    {
      "groupJid": "string",
      "subject": "string",
      "participants": 15,
      "createdAt": "2024-01-01T00:00:00.000Z"
    }
  ]
}
```

### 4. Get Group Info

**GET** `/whatsapp/groups/info/:instanceId?accountId=string&groupJid=string`

Gets detailed information about a specific group.

**Headers:**
- `Authorization: Bearer <token>` (required)

**Parameters:**
- `instanceId` (path): WhatsApp instance ID
- `accountId` (query): Account ID
- `groupJid` (query): Group JID

**Response:**
```json
{
  "error": false,
  "data": {
    "groupJid": "string",
    "subject": "string",
    "description": "string",
    "participants": [...],
    "createdAt": "2024-01-01T00:00:00.000Z"
  }
}
```

### 5. Add Group Participants

**POST** `/whatsapp/groups/add-participants/:instanceId`

Adds participants to an existing group.

**Headers:**
- `Authorization: Bearer <token>` (required)

**Parameters:**
- `instanceId` (path): WhatsApp instance ID

**Body:**
```json
{
  "accountId": "string",
  "groupJid": "string",
  "participants": ["phone1", "phone2", "..."]
}
```

**Response:**
```json
{
  "error": false,
  "data": {
    "success": true,
    "message": "Added 5 participants to group",
    "addedParticipants": ["phone1", "phone2", "..."]
  }
}
```

## 🔧 ShotX Integration Endpoints

### Create Multiple Groups (ShotX)

**POST** `/shotx/groups/create-multiple`

Same as the WhatsApp endpoint but integrated with the ShotX platform.

### List Groups (ShotX)

**GET** `/shotx/groups/list/:instanceId?accountId=string`

Same as the WhatsApp endpoint but integrated with the ShotX platform.

## 🔗 Evolution API Integration

The API is now integrated with the real Evolution API for WhatsApp group creation:

### Real Evolution API Endpoint:
```bash
curl --request POST \
  --url https://{server-url}/group/create/{instance} \
  --header 'Content-Type: application/json' \
  --header 'apikey: <api-key>' \
  --data '{
    "subject": "Group Name",
    "description": "Group Description",
    "participants": ["*************", "*************"]
  }'
```

### Mock Implementation (Development)

For development and testing, the API includes a mock implementation that:
- Simulates realistic API delays (1-3 seconds)
- Has a 10% failure rate for testing error handling
- Generates realistic mock data
- Automatically activates when `NODE_ENV=development` or when `WHATSAPP_API_URL` is not set

## 🔄 Integration with Frontend (QIP-527)

The API is designed to support the 4-step workflow from the frontend:

1. **Step 1**: Frontend selects WhatsApp instance
2. **Step 2**: Frontend selects leads (up to 5000)
3. **Step 3**: Frontend configures group prefix
4. **Step 4**: Frontend calls `/whatsapp/groups/create-multiple/:instanceId` to create groups

## 🚀 Usage Examples

### Creating Multiple Groups for 2500 Leads

```javascript
const response = await fetch('/whatsapp/groups/create-multiple/instance123', {
  method: 'POST',
  headers: {
    'Authorization': 'Bearer your-token',
    'Content-Type': 'application/json'
  },
  body: JSON.stringify({
    accountId: 'account123',
    userId: 'user123',
    groupPrefix: 'Sales Team',
    maxLeadsPerGroup: 500,
    leads: [
      {
        id: 'lead1',
        firstName: 'John',
        lastName: 'Doe',
        phone: '*************',
        displayName: 'John Doe'
      },
      // ... 2499 more leads
    ]
  })
})

const result = await response.json()
// Will create 5 groups: "Sales Team 1", "Sales Team 2", etc.
```

## 🔮 Future Enhancements

✅ **Completed**: Integration with real Evolution API for group creation

**Next Steps**:
1. Add webhook handling for group creation status updates
2. Implement enhanced rate limiting and retry logic
3. Add group persistence to Firebase for historical tracking
4. Add group editing and management features (rename, remove participants, etc.)
5. Add bulk operations for multiple group management
6. Implement group templates and scheduling

## 📝 Notes

- All endpoints require authentication via `AuthMiddleware`
- Phone numbers should be in international format (e.g., "*************")
- Group names are automatically generated as `{prefix} {number}`
- Maximum 500 leads per group (configurable)
- 2-second delay between group creations to avoid rate limiting
