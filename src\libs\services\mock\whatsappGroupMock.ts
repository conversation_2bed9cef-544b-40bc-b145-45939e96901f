import { CreateGroupResponse } from '../../types/whatsapp/group'
import { WhatsappServiceReponseType } from '../../types/whatsapp/Response'

/**
 * Mock implementation for WhatsApp group creation
 * This can be used for testing until the actual Evolution API integration is ready
 */
export class WhatsAppGroupMockService {
  
  async createGroup(
    instanceId: string,
    accountId: string,
    groupName: string,
    description: string = '',
    members: string[]
  ): Promise<WhatsappServiceReponseType<CreateGroupResponse>> {
    
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 1000 + Math.random() * 2000))
    
    // Simulate some failures for testing
    const shouldFail = Math.random() < 0.1 // 10% failure rate
    
    if (shouldFail) {
      return {
        error: true,
        status: 500,
        message: 'Mock: Failed to create group',
        data: {
          success: false,
          error: 'Mock: Group creation failed',
          message: 'Mock: Failed to create group'
        }
      }
    }
    
    // Generate mock group ID and JID
    const groupId = `mock_group_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
    const groupJid = `${groupId}@g.us`
    
    return {
      error: false,
      status: 200,
      data: {
        success: true,
        groupId,
        groupJid,
        message: 'Mock: Group created successfully'
      }
    }
  }
  
  async addGroupParticipants(
    instanceId: string,
    accountId: string,
    groupJid: string,
    participants: string[]
  ): Promise<WhatsappServiceReponseType> {
    
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 500 + Math.random() * 1000))
    
    return {
      error: false,
      status: 200,
      data: {
        success: true,
        message: `Mock: Added ${participants.length} participants to group`,
        addedParticipants: participants
      }
    }
  }
  
  async getGroupInfo(
    instanceId: string,
    accountId: string,
    groupJid: string
  ): Promise<WhatsappServiceReponseType> {
    
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 300))
    
    return {
      error: false,
      status: 200,
      data: {
        groupJid,
        subject: 'Mock Group',
        description: 'This is a mock group for testing',
        participants: [
          { id: '<EMAIL>', admin: 'admin' },
          { id: '<EMAIL>', admin: null }
        ],
        createdAt: new Date().toISOString()
      }
    }
  }
  
  async listGroups(
    instanceId: string,
    accountId: string
  ): Promise<WhatsappServiceReponseType> {
    
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 500))
    
    const mockGroups = [
      {
        groupJid: '<EMAIL>',
        subject: 'Mock Group 1',
        participants: 15,
        createdAt: new Date(Date.now() - ********).toISOString() // 1 day ago
      },
      {
        groupJid: '<EMAIL>',
        subject: 'Mock Group 2', 
        participants: 23,
        createdAt: new Date(Date.now() - *********).toISOString() // 2 days ago
      }
    ]
    
    return {
      error: false,
      status: 200,
      data: mockGroups
    }
  }
}

export const mockGroupService = new WhatsAppGroupMockService()
