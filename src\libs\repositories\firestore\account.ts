import { firestore } from '../../../config'
import { FIRESTORE_COLLECTIONS } from '../../../constants/collections'

export class AccountRepository {
  private readonly collectionRef = firestore
    .collection(FIRESTORE_COLLECTIONS.ACCOUNTS)

  all = async () => {
    const snapshot = await this.collectionRef.get()
    return snapshot.docs.map((doc) => doc.data())
  }

  get = async (accountId: string) => {
    const response = {
      error: false,
      message: '',
      data: null as any | null,
    }

    const accountDoc = this.collectionRef.doc(accountId)

    const account = await accountDoc.get()
    response.data = account.data()

    return response
  }

  getAccountIDByInstagramId = async (ig_id: string): Promise<string | null> => {
    // console.log('GETTING ACCOUNT BY', ig_id);

    try {
      const accounts = await this.collectionRef
        .where('ig_ids', 'array-contains', ig_id)  // 'ig_ids' deve ser um campo existente
        .get();

      if (!accounts.empty) {
        return accounts.docs[0].id;
      } else {
        return null;
      }
    } catch (error) {
      console.error('Error fetching account:', error);
      return null;
    }
  };

  update = async (accountId: string, account: any) => {
    const response = {
      error: false,
      message: '',
      data: null as any | null,
    }

    const accountDoc = this.collectionRef.doc(accountId)

    return accountDoc
      .set(account, { merge: true })
      .then(() => {
        console.log('Document successfully updated!')
        response.data = account
        return response
      })
      .catch((error) => {
        console.error('Error updating document: ', error)
        response.error = true
        response.message = error
        return response
      })
  }

  getByID = async (id: string) => {
    const response = {
      error: false,
      message: '',
      data: null as any | null,
    }

    const accountDoc = this.collectionRef.doc(id)

    const account = await accountDoc.get()
    response.data = account.data()

    return response
  }
}
