# WhatsApp Groups API - Usage Examples

Este documento fornece exemplos práticos de como usar a API de criação de grupos do WhatsApp implementada no QIP-526.

## 🚀 Exemplo Completo: Criando Grupos para 2500 Leads

### 1. Preparação dos Dados

```javascript
// Exemplo de dados de leads vindos do frontend
const leadsData = [
  {
    id: 'lead_001',
    firstName: '<PERSON>',
    lastName: '<PERSON>',
    phone: '*************',
    displayName: '<PERSON>'
  },
  {
    id: 'lead_002', 
    firstName: '<PERSON>',
    lastName: '<PERSON>',
    phone: '*************',
    displayName: '<PERSON>'
  },
  // ... mais 2498 leads
]
```

### 2. Chamada da API

```javascript
const response = await fetch('/whatsapp/groups/create-multiple/instance123', {
  method: 'POST',
  headers: {
    'Authorization': 'Bearer seu-token-aqui',
    'Content-Type': 'application/json'
  },
  body: JSON.stringify({
    accountId: 'account123',
    userId: 'user123',
    groupPrefix: 'Vendas Q1 2024',
    maxLeadsPerGroup: 500, // Opcional, padrão é 500
    leads: leadsData
  })
})

const result = await response.json()
```

### 3. Resposta Esperada

```javascript
{
  "error": false,
  "data": {
    "success": true,
    "totalGroups": 5,
    "createdGroups": [
      {
        "id": "group_001",
        "name": "Vendas Q1 2024 1",
        "description": "Group created automatically with 500 members",
        "members": [...], // 500 leads
        "createdAt": "2024-01-15T10:30:00.000Z",
        "instanceId": "instance123",
        "accountId": "account123", 
        "groupJid": "<EMAIL>",
        "status": "created"
      },
      // ... mais 4 grupos
    ],
    "failedGroups": [],
    "message": "Created 5 out of 5 groups"
  }
}
```

## 🎯 Integração com Frontend (QIP-527)

### Workflow de 4 Etapas

```javascript
// Etapa 1: Seleção da Instância
const selectedInstance = 'instance123'

// Etapa 2: Seleção dos Leads (até 5000)
const selectedLeads = await getSelectedLeads() // Função do frontend

// Etapa 3: Configuração do Prefixo
const groupPrefix = 'Campanha Black Friday'

// Etapa 4: Criação dos Grupos
const createGroups = async () => {
  try {
    const response = await fetch(`/whatsapp/groups/create-multiple/${selectedInstance}`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${authToken}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        accountId: userAccountId,
        userId: currentUserId,
        groupPrefix,
        leads: selectedLeads
      })
    })
    
    const result = await response.json()
    
    if (result.error) {
      showError(result.data.message || 'Erro ao criar grupos')
      return
    }
    
    // Sucesso - mostrar resultados
    const { createdGroups, failedGroups, totalGroups } = result.data
    
    showSuccess(`${createdGroups.length} de ${totalGroups} grupos criados com sucesso!`)
    
    if (failedGroups.length > 0) {
      showWarning(`${failedGroups.length} grupos falharam na criação`)
    }
    
  } catch (error) {
    showError('Erro de conexão ao criar grupos')
  }
}
```

## 📱 Exemplos de Uso Específicos

### Criar Grupo Único

```javascript
const response = await fetch('/whatsapp/groups/create/instance123', {
  method: 'POST',
  headers: {
    'Authorization': 'Bearer token',
    'Content-Type': 'application/json'
  },
  body: JSON.stringify({
    accountId: 'account123',
    userId: 'user123',
    groupName: 'Equipe de Vendas',
    description: 'Grupo da equipe de vendas',
    members: ['*************', '*************', '*************']
  })
})
```

### Listar Grupos Existentes

```javascript
const response = await fetch('/whatsapp/groups/list/instance123?accountId=account123', {
  headers: {
    'Authorization': 'Bearer token'
  }
})

const groups = await response.json()
```

### Adicionar Participantes a Grupo Existente

```javascript
const response = await fetch('/whatsapp/groups/add-participants/instance123', {
  method: 'POST',
  headers: {
    'Authorization': 'Bearer token',
    'Content-Type': 'application/json'
  },
  body: JSON.stringify({
    accountId: 'account123',
    groupJid: '<EMAIL>',
    participants: ['*************', '*************']
  })
})
```

## 🔧 Integração ShotX

### Criar Grupos via ShotX

```javascript
const response = await fetch('/shotx/groups/create-multiple', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json'
  },
  body: JSON.stringify({
    instanceId: 'instance123',
    accountId: 'account123',
    userId: 'user123',
    groupPrefix: 'ShotX Campaign',
    leads: leadsArray
  })
})
```

## 🛡️ Tratamento de Erros

### Validação de Entrada

```javascript
const validateRequest = (data) => {
  const errors = []
  
  if (!data.instanceId) errors.push('Instance ID é obrigatório')
  if (!data.accountId) errors.push('Account ID é obrigatório')
  if (!data.userId) errors.push('User ID é obrigatório')
  if (!data.groupPrefix) errors.push('Prefixo do grupo é obrigatório')
  if (!Array.isArray(data.leads) || data.leads.length === 0) {
    errors.push('Lista de leads é obrigatória')
  }
  if (data.leads && data.leads.length > 5000) {
    errors.push('Máximo de 5000 leads permitido')
  }
  
  return {
    isValid: errors.length === 0,
    errors
  }
}
```

### Tratamento de Respostas

```javascript
const handleGroupCreationResponse = (response) => {
  if (response.error) {
    // Erro na requisição
    console.error('Erro na API:', response.data.message)
    return
  }
  
  const { success, createdGroups, failedGroups, message } = response.data
  
  if (success) {
    console.log('✅ Sucesso:', message)
    console.log(`📊 Grupos criados: ${createdGroups.length}`)
    
    if (failedGroups.length > 0) {
      console.warn(`⚠️ Grupos que falharam: ${failedGroups.length}`)
      failedGroups.forEach(group => {
        console.warn(`- ${group.name}: ${group.error}`)
      })
    }
  } else {
    console.error('❌ Falha geral:', message)
  }
}
```

## 🧪 Modo de Desenvolvimento

### Usando Mock Service

```javascript
// No desenvolvimento, o mock service é ativado automaticamente
// quando NODE_ENV=development ou WHATSAPP_API_URL não está definido

// Para forçar o uso do mock service:
process.env.NODE_ENV = 'development'

// O mock service simula:
// - Delays realistas (1-3 segundos)
// - Taxa de falha de 10% para testes
// - Dados de resposta realistas
```

## 📊 Monitoramento e Logs

### Logs da API

```javascript
// A API gera logs detalhados:
console.log('🗝️ Creating 5 groups for 2500 leads (max 500 per group)')
console.log('🗝️ Waiting 2000ms before creating next group...')
console.log('🗝️ Group creation response:', response.data)
```

### Métricas de Performance

```javascript
const startTime = Date.now()

// ... criação dos grupos ...

const endTime = Date.now()
const duration = endTime - startTime

console.log(`⏱️ Tempo total: ${duration}ms`)
console.log(`📊 Tempo médio por grupo: ${duration / totalGroups}ms`)
```

## 🔮 Próximos Passos

1. **Webhooks**: Implementar notificações de status em tempo real
2. **Persistência**: Salvar grupos criados no Firebase
3. **Retry Logic**: Implementar tentativas automáticas para grupos que falharam
4. **Rate Limiting**: Implementar controle de taxa mais sofisticado
5. **Templates**: Criar templates de grupos para reutilização

## 📝 Notas Importantes

- ✅ **Integração Real**: A API já está integrada com a Evolution API real
- 🔧 **Mock Automático**: Mock service ativa automaticamente em desenvolvimento
- 📱 **Limite WhatsApp**: Máximo 500 participantes por grupo (limitação do WhatsApp)
- ⏱️ **Rate Limiting**: Delay de 2 segundos entre criações para evitar bloqueios
- 🛡️ **Validação**: Validação completa de entrada e tratamento de erros
- 🔐 **Autenticação**: Todos os endpoints protegidos com AuthMiddleware
