import { Request, Response, Router } from 'express'
import { ShotxController } from '../controller/shotx.controller'

export const router = Router()

const RESOURCE = '/shotx'

router.post(
  `${RESOURCE}/broadcast/send`,
  async (req: Request, res: Response) => {
    const controller = new ShotxController()

    const { messages } = req.body
    console.log('MESSAGES', messages)

    controller.sendMessages(messages)

    res.send({
      error: false,
      //data: snipers,
    })
  }
)

// Group Management Routes for ShotX
router.post(
  `${RESOURCE}/groups/create-multiple`,
  async (req: Request, res: Response) => {
    const { instanceId, accountId, userId, groupPrefix, leads, maxLeadsPerGroup } = req.body

    if (!instanceId || !accountId || !userId || !groupPrefix || !leads) {
      res.status(400).send({
        error: true,
        message: 'instanceId, accountId, userId, groupPrefix and leads are required',
      })
      return
    }

    if (!Array.isArray(leads) || leads.length === 0) {
      res.status(400).send({
        error: true,
        message: 'leads must be a non-empty array',
      })
      return
    }

    try {
      // Import WhatsappController dynamically to avoid circular dependencies
      const { WhatsappController } = await import('../../../controllers')

      const response = await WhatsappController.createMultipleGroups({
        instanceId,
        accountId,
        userId,
        groupPrefix,
        leads,
        maxLeadsPerGroup: maxLeadsPerGroup || 500
      })

      res.send({
        error: !response.success,
        data: response,
      })
    } catch (error: any) {
      res.status(500).send({
        error: true,
        message: error.message,
      })
    }
  }
)

router.get(
  `${RESOURCE}/groups/list/:instanceId`,
  async (req: Request, res: Response) => {
    const { instanceId } = req.params
    const { accountId } = req.query

    if (!instanceId || !accountId) {
      res.status(400).send({
        error: true,
        message: 'instanceId and accountId are required',
      })
      return
    }

    try {
      // Import WhatsappController dynamically to avoid circular dependencies
      const { WhatsappController } = await import('../../../controllers')

      const response = await WhatsappController.listGroups(
        instanceId,
        accountId as string
      )

      res.send({
        error: response.error,
        data: response.data,
      })
    } catch (error: any) {
      res.status(500).send({
        error: true,
        message: error.message,
      })
    }
  }
)

export { router as shotxRouter }
