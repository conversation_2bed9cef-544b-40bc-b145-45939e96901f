import { ioSendData } from '../app'
import { EvolutionInstanceStatus } from '../enums/evolution/InstanceStatus'
import { InstanceStatusEnum } from '../enums/instance/Status'
import { InstancesRepository } from '../libs/repositories/firestore/instance'
import { EvolutionService } from '../libs/services/axios/evolution'
import { instanceCreateType } from '../libs/types/codechat/instanceCreate'
import { InstanceCreateResponse } from '../libs/types/evolution/instance/response/create'
import { WhatsappServiceReponseType } from '../libs/types/whatsapp/Response'
import LogsController from './logs'

class EvolutionController {
  private instanceExistCodes
  private logs
  constructor(
    private readonly service: EvolutionService,
    private readonly repository: InstancesRepository
  ) {
    this.instanceExistCodes = [200, 403, 500]
    this.logs = LogsController
  }

  async createInstance(
    instanceDoc: instanceCreateType
  ): Promise<
    WhatsappServiceReponseType<InstanceCreateResponse | instanceCreateType>
  > {
    const { id, accountId } = instanceDoc
    const apiResponse = await this.service.createInstance(id, accountId)

    if (apiResponse.error || !apiResponse.data) {
      console.log('🗝️ Failed to create instance', apiResponse)
      return apiResponse
    }
    const { data } = apiResponse

    instanceDoc.status.state = EvolutionInstanceStatus.CLOSE
    instanceDoc.auth.jwt = data.hash

    console.log('🗝️ Update instance databse', id)
    const updateResult = await this.repository.update(instanceDoc)
    console.log('🗝️ Update result', updateResult)

    if (!updateResult) {
      console.log('🗝️ Failed to update instance', id)
      return {
        error: true,
        status: 500,
        message: 'Erro ao salvar instância no Banco de Dados',
        data: updateResult,
      }
    }

    console.log('🗝️ Created instance', id)
    return {
      error: false,
      status: 200,
      message: 'Instância criada com sucesso',
      data: instanceDoc,
    }
  }

  async connectInstance(instanceDoc: instanceCreateType) {
    const { id, accountId, auth } = instanceDoc
    const jwt = auth?.jwt

    console.log('🗝️ Getting instance status', id)
    // Obtem o status da instância no codechat
    const statusResponse = await this.service.fetchInstances(id, accountId, jwt)
    const exists =
      !statusResponse.error &&
      this.instanceExistCodes.includes(statusResponse.status)

    console.log('instance exists', exists)

    const result = {
      error: false,
      status: 200,
      message: '',
      data: statusResponse,
      code: '',
    }

    const instanceStatus = statusResponse.data[0]

    // Se a instância não existe
    if (!exists) {
      console.log('🗝️ Evolution Instance not found, creating ...', id)
      // Cria a instância no codechat
      const createResult = await this.createInstance(instanceDoc)

      // Se houve erro ao criar
      if (createResult.error) {
        result.error = true
        result.status = 500
        result.message = 'Erro ao conectar instância'
        result.data = createResult
        return result
      }

      instanceDoc = createResult.data as any
      console.log('🗝️ Evolution Instance created', id)
    }

    // Se já estiver conectada
    if (instanceStatus?.connectionStatus === InstanceStatusEnum.CONNECTED) {
      console.log('🗝️ Evolution Instance already connected', id)
      await this.repository.update({
        ...instanceDoc,
        status: {
          ...instanceDoc.status,
          state: instanceStatus?.connectionStatus,
        },
      })
      ioSendData(id, { state: 'open' })
      return result
    }

    console.log('🗝️ Connecting instance', id)
    // Se a instância existe
    const connectResponse = await this.service.connect(id, accountId, jwt)
    const { data } = connectResponse

    // Se houve erro ao conectar
    if (connectResponse.error) {
      console.log('🗝️ Evolution Instance not connected', id)
      result.error = true
      result.status = 500
      result.message = 'Erro ao conectar instância'
      result.data = connectResponse
      return result
    }

    console.log('🗝️ Connection requested', id)
    // Se a instância foi conectada
    result.message = 'Conexão solicitada com sucesso'
    result.data = data
    result.code = data?.code

    ioSendData(id, {
      state: 'connecting',
      code: data?.code,
    })

    return result
  }

  async disconnectInstance(instanceDoc: instanceCreateType) {
    const {
      id,
      accountId,
      auth: { jwt },
      status: { state },
    } = instanceDoc
    const result = {
      error: false,
      status: 200,
      message: '',
      data: {},
    }

    const disconnectResponse = await this.service.disconnect(id, accountId, jwt)

    // Se a instância foi desconectada
    if (!disconnectResponse.error) {
      instanceDoc.status.state = 'close'

      this.logs.createLogs(
        { data: instanceDoc },
        'instance_disconnected',
        null,
        null
      )

      this.repository.update(instanceDoc)
      ioSendData(id, { state: 'close' })
      // Deletar a instância
      this.service.delete(id, accountId, jwt)
      return result
    }

    ioSendData(id, { state, data: disconnectResponse })
  }
}

export default new EvolutionController(
  new EvolutionService(),
  new InstancesRepository()
)
