# QiPlus Chat API

## Instalacão

### Clone o Projeto

```bash
git clone https://github.com/paulowender/qiplus-chat-api.git # clona o projeto
cd /qiplus_chat_api/ # diretorio do projeto
nvm use 20 # node 20
yarn # instala as dependências
```

### Configure o .env

Copie o arquivo `.env.example` e renomeie-o para `.env`
Defina as variáveis de ambiente no arquivo `.env`

### Executar Ambiente de Desenvolvimento

```bash
yarn dev # roda o projeto
```

### Executar Ambiente de Produção

```bash
yarn start # build e roda o projeto
yarn stop # para o projeto
```
