export type SniperType = {
  version: string;
  id: string;
  name: string;
  events: Event[];
  groups: Group[];
  edges: Edge[];
  variables: Variable[];
  theme: Settings;
  selectedThemeTemplateId: null;
  settings: Settings;
  createdAt: Date;
  updatedAt: Date;
  icon: null;
  folderId: string;
  publicId: string;
  customDomain: null;
  workspaceId: string;
  resultsTablePreferences: null;
  isArchived: boolean;
  isClosed: boolean;
  whatsAppCredentialsId: null;
  riskLevel: null;
}

export type Edge = {
  id: string;
  from: From;
  to: To;
}

export type From = {
  eventId: string;
}

export type To = {
  groupId: string;
}

export type Event = {
  id: string;
  outgoingEdgeId: string;
  graphCoordinates: GraphCoordinates;
  type: string;
}

export type GraphCoordinates = {
  x: number;
  y: number;
}

export type Group = {
  id: string;
  title: string;
  graphCoordinates: GraphCoordinates;
  blocks: Block[];
}

export type Block = {
  id: string;
  type: string;
  options: Options;
}

export type Options = {
  variableId: string;
}

export type Settings = {
}

export type Variable = {
  id: string;
  name: string;
  isSessionVariable: boolean;
}
