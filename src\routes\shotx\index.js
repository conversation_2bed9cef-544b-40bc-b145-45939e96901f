/**
 * Dasboard Routes
 */
import React from 'react'
import { Route, Switch } from 'react-router-dom'
// async components
import { ProtectedPage } from '../../hooks/ProtectPage'
import { AddShotxBroadcast } from './broadcast'
import { AnalyticsBroadcast } from './broadcast/analytics'
import { ListShotxBroadcast } from './broadcast/list'
import { config } from './config'
import { ModuleContextProvider } from './contexts/ModuleContext'
import WhatsAppGroupManager from './groups/manager'
import Interactions from './interactions'
import Quick_messages from './quick_messages'
import { SniperRoute } from './snipers/route'
import { ModuleView } from './view'

const Dashboard = ({ match }) => (

  <div className="dashboard-wrapper">
    <Switch>
      <Route exact path={`${match.url}/`} >
        <ModuleContextProvider config={config} match={match}>
          <ModuleView match={match} />
        </ModuleContextProvider>
      </Route>
      <Route exact path={`${match.url}/quick_messages`}>
        <Quick_messages match={match} />
      </Route>
      <Route exact path={`${match.url}/groups/manager`}>
        <WhatsAppGroupManager match={match} />
      </Route>
      <Route exact path={`${match.path}/broadcast/add`}>
        <AddShotxBroadcast match={match} view={false} edit={false} />
      </Route>
      <Route exact path={`${match.path}/broadcast/`}>
        <ListShotxBroadcast match={match} />
      </Route>
      <Route exact path={`${match.url}/interactions/:id`}>
        <Interactions match={match} />
      </Route>
      <Route exact path={`${match.path}/broadcast/view/:id`}>
        <ProtectedPage path={`/shotx-cron/:id`} pathToVerify={"instance"} route={`${match.path}/broadcast`}>
          <AddShotxBroadcast match={match} />
        </ProtectedPage>
      </Route>
      <Route exact path={`${match.path}/broadcast/edit/:id`}>
        <ProtectedPage path={`/shotx-cron/:id`} pathToVerify={"instance"} route={`${match.path}/broadcast`}>
          <AddShotxBroadcast match={match} />
        </ProtectedPage>
      </Route>
      <Route exact path={`${match.path}/broadcast/analytics/:id`}>
        <ProtectedPage path={`/shotx-cron/:id`} pathToVerify={"instance"} route={`${match.path}/broadcast`}>
          <AnalyticsBroadcast match={match} />
        </ProtectedPage>
      </Route>
      <SniperRoute match={match} />
    </Switch>
  </div>
)

export default Dashboard
