import moment from 'moment'

export const dateFromNum = (num: number) => {
  return new Date(num * 1000)
}

export const dateToNum = (date: Date) => {
  return date.getTime() / 1000
}

export const formatDate = (date: Date, format = 'YYYY-MM-DD HH:mm:ss') => {
  return moment(date).format(format)
}

export const dateToString = (date: Date, format = 'YYYY-MM-DD HH:mm:ss') => {
  return formatDate(date, format)
}

export const convertTZ = (date: Date, tzString: string) => {
  return new Date(date.toLocaleString("en-US", { timeZone: tzString }));
}

export const getCurrentServerTime = () => {
  const date = new Date()
  return convertTZ(date, "America/Sao_Paulo")
}

export const getTimestampNow = () => {
  return moment().unix()
}