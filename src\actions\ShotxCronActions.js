/**
 * ShotxCron Actions
 */
import COLLECTIONS from 'Constants/AppCollections';
import { FirestoreRef } from 'FirebaseRef';
import { langMessages } from 'Lang/index';

export class ShotxCronActions {
    /**
     * Redux Action Save New Deal
     */
    scheduleActionSniper = async (data) => {
        return new Promise((resolve, reject) => FirestoreRef.collection(COLLECTIONS.SHOTX_CRON_COLLECTION_NAME).add(data).then(doc => {
            return resolve({
                data: {
                    data: {
                        id: doc.id,
                        error: false,
                        message: langMessages['shotx.snipers.scheduledMessage'],
                        schedule: true
                    }
                }
            }).catch(err => {
                return reject({
                    data: {
                        data: {
                            message: err.message,
                            error: true
                        }
                    }
                })
            })
        }
        ))
    }

    getLogsForBroadcast = async (broadcastId, startDate, endDate) => {
        return new Promise((resolve, reject) => {
            FirestoreRef.collection(COLLECTIONS.SHOTX_CRON_COLLECTION_NAME)
                .where('ID', '==', broadcastId).get().then(snapshot => {
                    if (snapshot.empty) {
                        return resolve([]);
                    }
                    const matchingDocsPromises = snapshot.docs.map(doc => {
                        return doc.ref.collection('logs')
                            .where('createdAt', '>=', (startDate / 1000)) // Filtro de início
                            .where('createdAt', '<=', (endDate / 1000)) // Filtro de fim
                            .get()
                            .then(logsSnapshot => {
                                const logsByStatus = logsSnapshot.docs.reduce((acc, logDoc) => {
                                    const logData = logDoc.data();
                                    const status = logData.trigger || 'unknown';


                                    if (!acc[status]) {
                                        acc[status] = [];
                                    }
                                    acc[status].push(logData);

                                    return acc;
                                }, {});

                                // Ordena os logs em cada status por createdAt (ascendente)
                                Object.keys(logsByStatus).forEach(status => {
                                    logsByStatus[status].sort((a, b) => {
                                        const dateA = a.createdAt.toDate ? a.createdAt.toDate() : a.createdAt;
                                        const dateB = b.createdAt.toDate ? b.createdAt.toDate() : b.createdAt;
                                        return dateA - dateB;
                                    });
                                });

                                return logsByStatus;
                            });
                    });

                    Promise.all(matchingDocsPromises)
                        .then(matchingDocs => resolve(matchingDocs))
                        .catch(err => reject({
                            data: {
                                data: {
                                    message: err.message,
                                    error: true
                                }
                            }
                        }));
                }).catch(err => {
                    return reject({
                        data: {
                            data: {
                                message: err.message,
                                error: true
                            }
                        }
                    });
                });
        });
    };

}
