type ImageMessageType = {
  keyId: string
  keyRemoteJid: string
  keyFromMe: boolean
  pushName: string
  content: {
    url: string
    mimetype: string
    caption: string
    fileSha256: string
    fileLength: string
    height: number
    width: number
    mediaKey: string
    fileEncSha256: string
    directPath: string
    mediaKeyTimestamp: string
    jpegThumbnail: string
    contextInfo: ObjectConstructor[]
  }
  isGroup: false
  messageType: string
  messageTimestamp: number
  device: string
}

export default ImageMessageType
