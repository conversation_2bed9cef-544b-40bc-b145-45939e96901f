{
    "env": {
        "es2021": true,
        "node": true
    },
    "extends": [
        "eslint:recommended",
        "plugin:@typescript-eslint/recommended",
        "prettier"
    ],
    "parser": "@typescript-eslint/parser",
    "parserOptions": {
        "ecmaVersion": "latest",
        "sourceType": "module"
    },
    "plugins": [
        "node",
        "@typescript-eslint",
        "prettier"
    ],
    "rules": {
        "prettier/prettier": [
            "error",
            {
                "endOfLine": "auto"
            }
        ],
        // "@typescript-eslint/no-unused-vars": "error",
        "@typescript-eslint/no-explicit-any": "off"
    },
    "ignorePatterns": [
        "dist/**"
    ]
}