import { Request, Response, Router } from 'express'
import multer from 'multer'
import { InstagramController } from '../../controllers'
import { InstanceStatusEnum } from '../../enums/instance/Status'

const uploadFile = multer({
  preservePath: true,
  limits: {
    fileSize: 1000000 * 10,
  },
})

export const router = Router()

const RESOURCE = '/instagram'

const key = process.env.INSTAGRAM_WEBHOOK_VALIDATION_KEY

//ROUTE CHECK AUTH IN FACEBOOK
router.get(
  `${RESOURCE}/webhook`,
  //AuthMiddleware,
  async (req: Request, res: Response) => {
    const { query } = req

    const mode = query['hub.mode']
    const challenge = query['hub.challenge']
    const token = query['hub.verify_token']
    console.log('🚀 ~ INSTAGRAM VALIDATION WEBHOOK', query)
    if (mode === 'subscribe' && token === key) {
      return res.send(challenge)
    }

    return res.status(400).send('Error, wrong token')
  }
)

//ROUTE OF LOGIN
router.get(
  `${RESOURCE}/auth/login/:accountId/:instanceId`,
  //AuthMiddleware,
  async (req: Request, res: Response) => {
    const { accountId, instanceId } = req.params

    if (instanceId && accountId) {
      const authenticationUrl = await InstagramController.auth(
        accountId,
        instanceId
      )

      if (authenticationUrl) {
        console.log('AUTH URL ', authenticationUrl)
        return res.redirect(authenticationUrl)
      }
    }

    return res.send('<h2>Falha na autenticação</h2>')
  }
)

//ROUTE TO AUTH
router.get(
  `${RESOURCE}/auth`,
  //AuthMiddleware,
  async (req: Request, res: Response) => {
    const { query } = req
    const { code, state } = query

    const states = String(state).split('_')
    const accountId = states[0]?.replace('accountId', '')
    const instanceId = states[1]?.replace('instanceId', '')
    const hasConnection = states[2]?.replace('hasConnection', '')

    console.log(`🚀 ~ INSTAGRAM WEBHOOK RECEIVED AUTH CALLBACK`, {
      accountId,
      instanceId,
      hasConnection,
      code,
    })

    if (!code) {
      await InstagramController.updateStatus(
        instanceId,
        accountId,
        InstanceStatusEnum.REFUSED
      )
      return res.render('instagram-fail.ejs')
    }

    if (instanceId && accountId) {
      const response = await InstagramController.authCallback(
        accountId,
        instanceId,
        String(code)
      )

      if (response.error) {
        await InstagramController.updateStatus(
          instanceId,
          accountId,
          InstanceStatusEnum.REFUSED
        )
        return res.render('instagram-fail.ejs')
      }
      const { newInstance, longToken, userProfile, oldInstance, permissions } =
        response.data

      const state = JSON.stringify({
        newInstance,
        longToken,
        userProfile,
        oldInstance,
        permissions,
        accountId,
      })

      const message =
        'Conta do Instagram já está em uso em outra instância, deseja forçar a conexão nesta instância?'
      if (oldInstance && longToken) {
        //return res.redirect(`${RESOURCE}/force_login`)
        return res
          .set(
            'Content-Security-Policy',
            "default-src *; style-src 'self' http://* 'unsafe-inline'; script-src 'self' http://* 'unsafe-inline' 'unsafe-eval'"
          )
          .render('instagram-force-instance.ejs', { state, message })
      }
    }

    const username = ''
    const photoUrl = ''

    return res.render('instagram-success.ejs', { username, photoUrl })
  }
)

router.post(`${RESOURCE}/auth/force`, async (req: Request, res: Response) => {
  const { state, action } = req.body

  const {
    newInstance,
    longToken,
    userProfile,
    oldInstance,
    accountId,
    permissions,
  } = JSON.parse(state)

  const response = await InstagramController.authForce(
    newInstance,
    longToken,
    userProfile,
    oldInstance,
    accountId,
    permissions,
    action
  )

  return res.send(response)
})

router.put(`${RESOURCE}/edit`, async (req: Request, res: Response) => {
  const { instance } = req.body

  const response = await InstagramController.updateWebhooks(instance)

  return res.send(response)
})

//ROUTE WEBHOOK
router.post(`${RESOURCE}/webhook`, async (req: Request, res: Response) => {
  const body = req?.body

  if (body) {
    await InstagramController.webhookReceived(body)
  }

  return res.status(200).send('200 OK HTTPS')
})

router.get(
  `${RESOURCE}/logout`,
  //AuthMiddleware,
  async (req: Request, res: Response) => {
    console.log(`🚀 ~ INSTAGRAM WEBHOOK RECEIVED LOGOUT`, { req })

    return res.render('instagram-fail.ejs')
  }
)

router.delete(
  `${RESOURCE}/unsubscribe`,
  //AuthMiddleware,
  async (req: Request, res: Response) => {
    const { access_token, ig_id } = req.body
    console.log(`🚀 ~ INSTAGRAM UNSUBSCRIBE`, { access_token, ig_id })

    if (!access_token) {
      console.log(`🚀 ~ access_token is required`)
      return res.status(400).send({
        error: true,
        message: 'access_token is required',
      })
    }
    const result = await InstagramController.unsubscribe(access_token)
    return res.send(result)
  }
)

router.delete(
  `${RESOURCE}/auth/logout`,
  //AuthMiddleware,
  async (req: Request, res: Response) => {
    const { instance } = req.body

    console.log('instance', instance)

    if (instance) {
      const result = await InstagramController.logout(instance)
      return res.send(result)
    } else {
      return res.send('instance required')
    }
  }
)

router.post(
  `${RESOURCE}/sendmessage`,
  //AuthMiddleware,
  async (req: Request, res: Response) => {
    const { message, ig_id, instance } = req.body

    if (!message || !ig_id || !instance) {
      console.log(
        `🚀 ~ access_token, message, ig_id and instance object is required`
      )
      return res.status(400).send({
        error: true,
        message: 'access_token, message, ig_id is required',
      })
    }
    const result = await InstagramController.sendMessage(
      ig_id,
      message,
      instance
    )
    return res.send(result)
  }
)

router.post(
  `${RESOURCE}/media/send`,
  uploadFile.single('file'),
  // AuthMiddleware, // TODO: uncomment when auth is ready
  // RulesMiddleware, // TODO: uncomment when auth is ready
  async (req: Request, res: Response) => {
    const { ig_sid, type, mimetype, instance } = req.body
    let response: any
    const file = req.file

    if (!file || !instance || !ig_sid || !mimetype || !type) {
      res.status(400).send({
        error: true,
        data: 'instance, ig_sid, mimetype, type and file are required',
      })
      return
    }

    const instanceObj = JSON.parse(instance)
    response = await InstagramController.sendMedia(
      instanceObj,
      ig_sid,
      file,
      type
    )

    res.send({
      error: response?.error || false,
      data: response?.data,
    })
  }
)

router.post(
  `${RESOURCE}/message/broadcast`,
  async (req: Request, res: Response) => {
    // RabbitMQIntercept<any>({
    //   queueName: `${RESOURCE}/message/broadcast`,
    //   data: req.body,
    //   onQueue(sended) {
    //     res.send('ok')
    //   },
    // async onReceive(data) {
    const { shotxCrons } = req.body
    await InstagramController.sendBroadcast(shotxCrons)
    res.send('ok')
    // },
    // })
  }
)

export { router as instagramRouter }
