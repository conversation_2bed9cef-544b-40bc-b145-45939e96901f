import axios, { AxiosResponse } from 'axios'
import { makeInstanceName } from '../../../utils/codechatUtils'
import MessageReadType from '../../types/codechat/messageRead'
import { WhatsappServiceReponseType } from '../../types/whatsapp/Response'

export class CodechatService {
  private axios: any
  constructor() {
    this.axios = axios.create({
      baseURL: process.env.WHATSAPP_API_URL,
      headers: {
        'Content-Type': 'application/json',
        Apikey: process.env.WHATSAPP_API_TOKEN,
      },
    })
  }

  async createInstance(
    instanceId: string,
    accountId: string
  ): Promise<WhatsappServiceReponseType> {
    const instanceName = makeInstanceName(accountId, instanceId)
    console.log('🗝️ Creating instance', instanceName)
    const url = `/instance/create`
    const body = {
      instanceName
    }
    let result: WhatsappServiceReponseType
    try {
      const response: AxiosResponse = await this.axios.post(url, body)
      result = {
        error: false,
        status: response.status,
        data: response.data,
      }
    } catch (error: any) {
      result = {
        error: true,
        status: error?.response?.status ? error?.response?.status : '',
        data: {
          message: error.message,
        },
      }
    }
    return result
  }

  async fetchInstances(
    instanceId: string,
    accountId: string,
    jwt: string | null
  ): Promise<WhatsappServiceReponseType> {
    const instanceName = makeInstanceName(accountId, instanceId)
    console.log('🗝️ Fetching instance', instanceName)
    const url = `/instance/fetchInstances?instanceName=${instanceName}`
    let result: WhatsappServiceReponseType
    try {
      const response: AxiosResponse = await this.axios.get(url, {
        headers: {
          Authorization: `Bearer ${jwt}`,
        },
      })
      result = {
        error: false,
        status: response.status,
        data: response.data,
      }
    } catch (error: any) {
      result = {
        error: true,
        status: error?.response?.status ? error?.response?.status : '',
        data: {
          message: error.message,
        },
      }
    }
    return result
  }

  async status(
    instanceId: string,
    accountId: string,
    jwt: string | null
  ): Promise<WhatsappServiceReponseType> {
    const instanceName = makeInstanceName(accountId, instanceId)
    const url = `/instance/connectionState/${instanceName}`
    let result: WhatsappServiceReponseType

    return this.axios
      .get(url, {
        headers: {
          Authorization: `Bearer ${jwt}`,
        },
      })
      .then((response: any) => {
        result = {
          error: false,
          status: response.status,
          data: response.data,
        }
        return result
      })
      .catch((error: any) => {
        result = {
          error: true,
          status: error?.response?.status || '',
          data: {
            message: error.message,
          },
        }
        return result
      })
  }

  async connect(
    instanceId: string,
    accountId: string,
    jwt: string | null
  ): Promise<WhatsappServiceReponseType> {
    const instanceName = makeInstanceName(accountId, instanceId)
    console.log('🗝️ Connecting instance', instanceName)
    const url = `/instance/connect/${instanceName}`
    let result: WhatsappServiceReponseType
    return this.axios
      .get(url, {
        headers: {
          Authorization: `Bearer ${jwt}`,
        },
      })
      .then((response: any) => {
        result = {
          error: false,
          status: response.status,
          data: response.data,
        }
        return result
      })
      .catch((error: any) => {
        result = {
          error: true,
          status: error?.response?.status || '',
          data: {
            message: error.message,
          },
        }
        return result
      })
  }

  async disconnect(
    instanceId: string,
    accountId: string,
    jwt: string | null
  ): Promise<WhatsappServiceReponseType> {
    const instanceName = makeInstanceName(accountId, instanceId)
    console.log('🗝️ Disconnecting instance', instanceName)
    const url = `/instance/logout/${instanceName}`
    let result: WhatsappServiceReponseType
    try {
      const response: AxiosResponse = await this.axios.delete(url, {
        headers: {
          Authorization: `Bearer ${jwt}`,
        },
      })
      result = {
        error: false,
        status: response.status,
        data: response.data,
      }
    } catch (error: any) {
      result = {
        error: true,
        status: error?.response?.status ? error?.response?.status : '',
        data: {
          message: error.message,
        },
      }
    }
    return result
  }

  async delete(
    instanceId: string,
    accountId: string,
    jwt: string | null
  ): Promise<WhatsappServiceReponseType> {
    const instanceName = makeInstanceName(accountId, instanceId)
    console.log('🗝️ Deleting instance', instanceName)
    const url = `/instance/delete/${instanceName}`
    let result: WhatsappServiceReponseType
    try {
      const response: AxiosResponse = await this.axios.delete(url, {
        headers: {
          Authorization: `Bearer ${jwt}`,
        },
      })
      result = {
        error: false,
        status: response.status,
        data: response.data,
      }
      console.log('🗝️ Instance deleted')
    } catch (error: any) {
      result = {
        error: true,
        status: error?.response?.status ? error?.response?.status : '',
        data: {
          message: error.message,
        },
      }
      console.log('🗝️ Instance not deleted', error)
    }
    return result
  }

  async sendText(
    instanceId: string,
    accountId: string,
    phone: string,
    message: string
  ): Promise<WhatsappServiceReponseType> {
    const instanceName = makeInstanceName(accountId, instanceId)
    const url = `/message/sendText/${instanceName}`
    let result: WhatsappServiceReponseType
    const body = {
      number: phone,
      textMessage: {
        text: message,
      },
    }
    try {
      const response: AxiosResponse = await this.axios.post(url, body)
      result = {
        error: false,
        status: response.status,
        data: response.data,
      }
    } catch (error: any) {
      result = {
        error: true,
        status: error?.response?.status ? error?.response?.status : '',
        data: {
          message: error.message,
        },
      }
    }
    return result
  }

  async sendAudio(
    instanceId: string,
    accountId: string,
    phone: string,
    audio: string
  ) {
    const instanceName = makeInstanceName(accountId, instanceId)
    const url = `/message/sendWhatsAppAudio/${instanceName}`
    const body = {
      number: phone,
      audioMessage: {
        audio,
      },
    }
    try {
      const response: AxiosResponse = await this.axios.post(url, body)
      return response
    } catch (error: any) {
      return error
    }
  }
  async sendAudioFile(
    instanceId: string,
    accountId: string,
    phone: string,
    audio: any
  ) {
    const instanceName = makeInstanceName(accountId, instanceId)
    const url = `/message/sendWhatsAppAudioFile/${instanceName}`
    console.log(audio)
    const audioBlob = new Blob([audio.buffer])
    //const audioFile = fs.createReadStream(audio.buffer).toString()
    const formData = new FormData()

    formData.append('attachment', audioBlob, audio.originalname)
    formData.append('number', phone)
    try {

      return await this.axios.post(url, formData, {
        headers: { 'Content-Type': 'multipart/form-data' },
      }).then(async (sendResult: AxiosResponse) => {
        return await this.getBase64FromMediaMessage(instanceId, accountId, sendResult.data.key.id)
          .then(dataResult => {
            return { sendResult, dataResult }
          })
          .catch((err: AxiosResponse) => {
            return
          })
      }).catch((err: AxiosResponse) => {
        console.error(err)
        return
      })
    } catch (error: any) {
      console.log(error.response.data)
      return error
    }
  }

  async sendFile(
    instanceId: string,
    accountId: string,
    phone: string,
    file: any,
    type: string,
    caption?: string
  ) {
    const instanceName = makeInstanceName(accountId, instanceId)
    const url = `/message/sendMediaFile/${instanceName}`
    console.log(file);

    const fileBlob = new Blob([file.buffer], { type: file.mimetype })
    const formData = new FormData()

    formData.append('attachment', fileBlob, file.originalname)
    formData.append('number', phone)
    formData.append('mediatype', type)
    caption && formData.append('caption', caption)
    try {
      return await this.axios.post(url, formData, {
        headers: { 'Content-Type': 'multipart/form-data' },
      }).then(async (sendResult: AxiosResponse) => {
        if (type !== 'video') {
           return await this.getBase64FromMediaMessage(instanceId, accountId, sendResult.data.key.id)
            .then(dataResult => {
              return { sendResult, dataResult }
            })
            .catch((err: AxiosResponse) => {
              console.log('Fail to get message media', err)
              return { sendResult }
            })
        } else {
          let dataResult = {
            data: {
              mediaType: 'videoMessage'
            }
          }
          return { sendResult, dataResult }
        }
       
      }).catch((err: AxiosResponse) => {
        console.log('FAIL TO SEND FILE')
        console.error(err)
        return
      })
    } catch (error: any) {
      console.log(error.response.data)
      return error
    }
  }

  async markMessageAsRead(
    instanceId: string,
    accountId: string,
    readMessages: MessageReadType[]
  ): Promise<WhatsappServiceReponseType> {
    const instanceName = makeInstanceName(accountId, instanceId)
    const url = `/chat/markMessageAsRead/${instanceName}`
    let result: WhatsappServiceReponseType
    const body = { readMessages }

    try {
      const response: AxiosResponse = await this.axios.put(url, body)
      result = {
        error: false,
        status: response.status,
        data: response.data,
      }
    } catch (error: any) {
      result = {
        error: true,
        status: error?.response?.status ? error?.response?.status : '',
        data: {
          message: error.message,
        },
      }
    }
    return result
  }

  async retrieverMediaMessage(
    instanceId: string,
    accountId: string,
    messageId: string
  ) {
    const instanceName = makeInstanceName(accountId, instanceId)
    const url = `/chat/retrieverMediaMessage/${instanceName}`
    let result: WhatsappServiceReponseType
    const body = {
      keyId: messageId
    }
    try {
      const response: AxiosResponse = await this.axios.post(url, body)
      console.log(response.headers)
      console.log(response.headers['content-type'])
      return response
    }
    catch (error: any) {
      result = {
        error: true,
        status: error?.response?.status ? error?.response?.status : '',
        data: {
          message: error.message,
        },
      }
    }

    return result
  }

  async getBase64FromMediaMessage(
    instanceId: string,
    accountId: string,
    messageId: string
  ) {
    const instanceName = makeInstanceName(accountId, instanceId)
    const url = `/chat/getBase64FromMediaMessage/${instanceName}`
    let result: WhatsappServiceReponseType
    const body = {
      key: {
        id: messageId
      }
    }
    try {
      const response: AxiosResponse = await this.axios.post(url, body)
      result = {
        error: false,
        status: response.status,
        data: response.data,
      }
    }
    catch (error: any) {
      result = {
        error: true,
        status: error?.response?.status ? error?.response?.status : '',
        data: {
          message: error.message,
        },
      }
    }

    return result
  }

  async sniperConnect(
    instanceId: string,
    accountId: string,
    publicSniperId: string,
    enabled: boolean,
    jwt: string,
    findIntegration: any
  ) {
    const instanceName = makeInstanceName(accountId, instanceId)
    const urlCreate = `/typebot/create/${instanceName}`
    const urlUpdate = `/typebot/update/${instanceName}`
    let result: WhatsappServiceReponseType
    const body = {
      publicId: publicSniperId,
      typebotUrl: process.env.SNIPER_URL,
      enabled
    }

    const headers = {
      headers: {
        Authorization: `Bearer ${jwt}`,
      }
    }

    try {
      const response: AxiosResponse = findIntegration.data === null ? await this.axios.post(urlCreate, body, headers) : await this.axios.put(urlUpdate, body, headers)
      result = {
        error: false,
        status: response.status,
        data: response.data
      }
    }
    catch (error: any) {
      result = {
        error: true,
        status: error?.response?.status ? error?.response?.status : '',
        data: {
          message: error.message,
        },
      }
    }

    return result
  }

  async findIntegration(
    instanceId: string,
    accountId: string,
    jwt: string
  ): Promise<WhatsappServiceReponseType> {
    const instanceName = makeInstanceName(accountId, instanceId)
    const url = `/typebot/find/${instanceName}`
    let result: WhatsappServiceReponseType

    const headers = {
      headers: {
        Authorization: `Bearer ${jwt}`,
      }
    }

    try {
      const response: AxiosResponse = await this.axios.get(url, headers)
      result = {
        error: false,
        status: response.status,
        data: response.data
      }
    }
    catch (error: any) {
      result = {
        error: true,
        status: error?.response?.status ? error?.response?.status : '',
        data: {
          message: error.message,
        },
      }
    }

    return result
  }

  async getSession(
    instanceId: string,
    accountId: string,
    remoteJid: string,
    jwt: string
  ) {
    const instanceName = makeInstanceName(accountId, instanceId)
    const url = `/typebot/last_session/find/${instanceName}`
    let result: WhatsappServiceReponseType

    const headers = {
      headers: {
        Authorization: `Bearer ${jwt}`,
      }
    }

    const body = {remoteJid}

    try {
      const response: AxiosResponse = await this.axios.post(url, body, headers)
      result = {
        error: false,
        status: response.status,
        data: response.data
      }
    }
    catch (error: any) {
      result = {
        error: true,
        status: error?.response?.status ? error?.response?.status : '',
        data: {
          message: error.message,
        },
      }
    }

    return result
  }

  async updateSession(
    instanceId: string,
    accountId: string,
    remoteJid: string,
    sessionId: string,
    action: string,
    jwt: string
  ) {
    const instanceName = makeInstanceName(accountId, instanceId)
    const url = `/typebot/sessions/update/${instanceName}`
    let result: WhatsappServiceReponseType

    const headers = {
      headers: {
        Authorization: `Bearer ${jwt}`,
      }
    }

    const body = {
      sessionId,
      remoteJid,
      action
    }

    try {
      const response: AxiosResponse = await this.axios.patch(url, body, headers)
      result = {
        error: false,
        status: response.status,
        data: response.data
      }
    }
    catch (error: any) {
      result = {
        error: true,
        status: error?.response?.status ? error?.response?.status : '',
        data: {
          message: error.message,
        },
      }
    }

    return result
  }

  async whatsappNumbers(
    instanceId: string,
    accountId: string,
    phone: string,
    jwt: string
  ): Promise<WhatsappServiceReponseType> {
    const instanceName = makeInstanceName(accountId, instanceId)
    const url = `/chat/whatsappNumbers/${instanceName}`
    let result: WhatsappServiceReponseType

    const headers = {
      headers: {
        Authorization: `Bearer ${jwt}`,
      }
    }

    const body = {
      numbers: [
        phone
      ]
    }

    try {
      const response: AxiosResponse = await this.axios.post(url, body, headers)
      result = {
        error: false,
        status: response.status,
        data: response.data[0]
      }
    }
    catch (error: any) {
      result = {
        error: true,
        status: error?.response?.status ? error?.response?.status : '',
        data: {
          message: error.message,
        },
      }
    }

    return result
  }

  async webhook(
    instanceId: string,
    accountId: string
  ): Promise<WhatsappServiceReponseType> {
    const instanceName = makeInstanceName(accountId, instanceId)
    const url = `/webhook/set/${instanceName}`
    let result: WhatsappServiceReponseType
    const body = {
      enabled: true,
      url: `${process.env.WHATSAPP_QICHAT_WEBHOOK}`,
    }
    try {
      const response: AxiosResponse = await this.axios.post(url, body)
      result = {
        error: false,
        status: response.status,
        data: response.data,
      }
    } catch (error: any) {
      result = {
        error: true,
        status: error?.response?.status ? error?.response?.status : '',
        data: {
          message: error.message,
        },
      }
    }
    return result
  }
}

export const CodechatServiceType = typeof CodechatService
