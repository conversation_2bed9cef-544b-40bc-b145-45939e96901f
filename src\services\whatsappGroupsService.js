/**
 * WhatsApp Groups Service
 * Service para criação de grupos WhatsApp via chat-api
 */
import { getCurrentUserToken } from 'FirebaseRef'
import { axiosChatService } from '../routes/chat/API/axiosChatService'
import { langMessages } from '../lang'

/**
 * Cria um único grupo WhatsApp
 * @param {Object} params - Parâmetros para criação do grupo
 * @param {string} params.instanceId - ID da instância WhatsApp
 * @param {string} params.accountId - ID da conta
 * @param {string} params.userId - ID do usuário
 * @param {string} params.groupName - Nome do grupo
 * @param {string} params.description - Descrição do grupo (opcional)
 * @param {Array<string>} params.members - Array com números de telefone dos membros
 * @returns {Promise<Object>} Resultado da criação do grupo
 */
export const createWhatsAppGroup = async ({ instanceId, accountId, userId, groupName, description = '', members }) => {
  if (!instanceId || !accountId || !userId || !groupName || !members || !Array.isArray(members)) {
    throw new Error('Parâmetros obrigatórios: instanceId, accountId, userId, groupName, members')
  }

  try {
    const token = await getCurrentUserToken()
    const url = `/whatsapp/groups/create/${instanceId}`
    
    const body = {
      accountId,
      userId,
      groupName,
      description,
      members
    }

    const response = await axiosChatService.post(url, body, {
      headers: {
        'Content-Type': 'application/json',
        Authorization: `Bearer ${token}`,
      }
    })

    if (response.status === 200 || response.status === 201) {
      return {
        success: true,
        data: response.data?.data || response.data,
        error: false
      }
    }

    return {
      success: false,
      error: true,
      message: response.data?.message || 'Erro ao criar grupo',
      data: response.data
    }

  } catch (error) {
    console.error('Erro ao criar grupo WhatsApp:', error)
    
    return {
      success: false,
      error: true,
      message: error.response?.data?.message || error.message || langMessages['whatsapp.groups.error.createGroups'],
      status: error.response?.status,
      data: error.response?.data
    }
  }
}

/**
 * Cria múltiplos grupos WhatsApp
 * @param {Object} params - Parâmetros para criação dos grupos
 * @param {string} params.instanceId - ID da instância WhatsApp
 * @param {string} params.accountId - ID da conta
 * @param {string} params.userId - ID do usuário
 * @param {string} params.groupPrefix - Prefixo dos grupos
 * @param {Array<Object>} params.leads - Array com dados dos leads
 * @param {number} params.maxLeadsPerGroup - Máximo de leads por grupo (padrão: 500)
 * @returns {Promise<Object>} Resultado da criação dos grupos
 */
export const createMultipleWhatsAppGroups = async ({ 
  instanceId, 
  accountId, 
  userId, 
  groupPrefix, 
  leads, 
  maxLeadsPerGroup = 500 
}) => {
  if (!instanceId || !accountId || !userId || !groupPrefix || !leads || !Array.isArray(leads)) {
    throw new Error('Parâmetros obrigatórios: instanceId, accountId, userId, groupPrefix, leads')
  }

  if (leads.length === 0) {
    throw new Error('Array de leads não pode estar vazio')
  }

  try {
    const token = await getCurrentUserToken()
    const url = `/whatsapp/groups/create-multiple/${instanceId}`
    
    const body = {
      accountId,
      userId,
      groupPrefix,
      leads,
      maxLeadsPerGroup
    }

    const response = await axiosChatService.post(url, body, {
      headers: {
        'Content-Type': 'application/json',
        Authorization: `Bearer ${token}`,
      }
    })

    if (response.status === 200 || response.status === 201) {
      return {
        success: true,
        data: response.data?.data || response.data,
        error: false
      }
    }

    return {
      success: false,
      error: true,
      message: response.data?.message || 'Erro ao criar grupos',
      data: response.data
    }

  } catch (error) {
    console.error('Erro ao criar múltiplos grupos WhatsApp:', error)
    
    return {
      success: false,
      error: true,
      message: error.response?.data?.message || error.message || langMessages['whatsapp.groups.error.createGroups'],
      status: error.response?.status,
      data: error.response?.data
    }
  }
}

/**
 * Obtém informações de um grupo WhatsApp
 * @param {Object} params - Parâmetros para obter informações do grupo
 * @param {string} params.instanceId - ID da instância WhatsApp
 * @param {string} params.accountId - ID da conta
 * @param {string} params.groupJid - JID do grupo WhatsApp
 * @returns {Promise<Object>} Informações do grupo
 */
export const getWhatsAppGroupInfo = async ({ instanceId, accountId, groupJid }) => {
  if (!instanceId || !accountId || !groupJid) {
    throw new Error('Parâmetros obrigatórios: instanceId, accountId, groupJid')
  }

  try {
    const token = await getCurrentUserToken()
    const url = `/whatsapp/groups/info/${instanceId}`
    
    const body = {
      accountId,
      groupJid
    }

    const response = await axiosChatService.post(url, body, {
      headers: {
        'Content-Type': 'application/json',
        Authorization: `Bearer ${token}`,
      }
    })

    if (response.status === 200) {
      return {
        success: true,
        data: response.data?.data || response.data,
        error: false
      }
    }

    return {
      success: false,
      error: true,
      message: response.data?.message || 'Erro ao obter informações do grupo',
      data: response.data
    }

  } catch (error) {
    console.error('Erro ao obter informações do grupo WhatsApp:', error)
    
    return {
      success: false,
      error: true,
      message: error.response?.data?.message || error.message || 'Erro ao obter informações do grupo',
      status: error.response?.status,
      data: error.response?.data
    }
  }
}

/**
 * Service principal para gestão de grupos WhatsApp
 */
export const WhatsAppGroupsService = {
  createGroup: createWhatsAppGroup,
  createMultipleGroups: createMultipleWhatsAppGroups,
  getGroupInfo: getWhatsAppGroupInfo
}

export default WhatsAppGroupsService
