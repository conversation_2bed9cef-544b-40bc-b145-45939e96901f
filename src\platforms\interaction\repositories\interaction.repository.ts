import { firestore } from '../../../config';
import { FIRESTORE_COLLECTIONS } from '../../../constants/collections';

export class InteractionRepository {

  contacts(accountId: string, instanceId: string) {
    return firestore
      .collection(FIRESTORE_COLLECTIONS.SHOTX)
      .doc(accountId)
      .collection(FIRESTORE_COLLECTIONS.INSTANCES)
      .doc(instanceId)
      .collection(FIRESTORE_COLLECTIONS.CONTACTS)
  }

  get = async (accountId: string, instanceId: string, ig_sid: string) => {

    try {
      // Executa a consulta
      const querySnapshot = await this.contacts(accountId, instanceId).doc(ig_sid).get();
      if (!querySnapshot.exists) return

      // Retorna os documentos encontrados
      return querySnapshot.data()
    } catch (error: any) {
      console.log(`🚀 - Failed to get contact ${ig_sid}: `, error);
      return
    }
  }

  vinculateLead = async (accountId: string, instanceId: string, ig_sid: string, leadId: string) => {
    if (!leadId) {
      console.log('Lead not vinculated, id not found', { leadId })
      return
    }
    return this.contacts(accountId, instanceId).doc(ig_sid).set({ lead_id: leadId }, { merge: true })
  }

}
