{"editor.codeActionsOnSave": {"source.fixAll.eslint": "explicit", "source.organizeImports": "explicit"}, "editor.autoClosingBrackets": "always", "editor.defaultFormatter": "esbenp.prettier-vscode", "editor.formatOnPaste": true, "editor.formatOnSave": true, "editor.formatOnType": true, "editor.inlineSuggest.enabled": true, "editor.tabSize": 2, "files.insertFinalNewline": true, "files.trimTrailingWhitespace": true, "[javascript]": {"editor.formatOnSave": true, "editor.formatOnType": true, "editor.formatOnPaste": true, "editor.defaultFormatter": "esbenp.prettier-vscode", "editor.autoClosingBrackets": "always"}, "[javascriptreact]": {"editor.formatOnSave": true, "editor.formatOnType": true, "editor.formatOnPaste": true, "editor.defaultFormatter": "esbenp.prettier-vscode", "editor.autoClosingBrackets": "always"}, "[jsx]": {"editor.formatOnSave": true, "editor.formatOnType": true, "editor.formatOnPaste": true, "editor.defaultFormatter": "esbenp.prettier-vscode", "editor.autoClosingBrackets": "always"}, "[typescript]": {"editor.formatOnSave": true, "editor.formatOnType": true, "editor.formatOnPaste": true, "editor.defaultFormatter": "esbenp.prettier-vscode", "editor.autoClosingBrackets": "always"}, "[typescriptreact]": {"editor.formatOnSave": true, "editor.formatOnType": true, "editor.formatOnPaste": true, "editor.defaultFormatter": "esbenp.prettier-vscode", "editor.wordWrap": "wordWrapColumn", "editor.wordWrapColumn": 260}}