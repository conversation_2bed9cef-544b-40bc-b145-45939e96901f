export const LeadModel = {
  id: '',
  ID: '',
  accountId: '', // need
  address: {
    country: '',
    num: '',
    neighborhood: '',
    comp: '',
    ref: '',
    postalCode: '',
    city: '',
    street: '',
    state: '',
  },
  affiliateId: '',
  author: '',
  avatar: '',
  birth: {},
  birthday: '',
  cnpj: '',
  collection: 'leads', // need
  comments: [],
  companyName: '',
  context: null,
  cpf: '',
  customFields: {},
  date: '',
  description: '',
  displayName: '',
  email: null,
  external: null,
  facebook: '',
  firstName: '', // need
  gender: '',
  ie: '',
  instagram: '',
  lastName: '',
  level: 100,
  linkedin: '',
  ig_sids: [],
  ig_contacts: [],
  locale: 'pt_BR',
  logs: {
    added: {
      operator_id: '',
      date: '',
      user: '',
    },
  },
  mailbox: {},
  mailbox_verified: null,
  manager: '',
  maritalStatus: '',
  message: 'createLead > newLead',
  mobile: '', // need
  mobileCC: '', // need
  modified: '',
  nickname: '',
  occupation: '',
  owner: '',
  phone: '',
  phoneCC: '',
  profile: 'n',
  promoter: '',
  pts: 0,
  pts_gained: 0,
  qrcode: '',
  qualification: '',
  rg: '',
  roles: ['lead'],
  score: 0,
  score_gained: 0,
  seller: '',
  shipping: {
    state: '',
    street: '',
    ref: '',
    city: '',
    country: '',
    comp: '',
    num: '',
    postalCode: '',
    neighborhood: '',
  },
  status: 'publish',
  stores: [],
  tags: [],
  twitter: '',
  type: 'individual',
  url: '',
}
