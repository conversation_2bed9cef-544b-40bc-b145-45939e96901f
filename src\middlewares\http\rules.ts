/**
 * User Middlewares
 *
 * Middleware para verificar se o usuário tem permissão
 *
 * */

import { NextFunction, Request, Response } from 'express'
import { InstancesRepository } from '../../libs/repositories/firestore/instance'
import { userById } from '../../libs/services/firestore/qiuser'

export const RulesMiddleware = async (
  req: Request,
  res: Response,
  next: NextFunction
) => {
  const user = req.user
  const { accountId, userId } = req.body

  const _authUserId = String(user.uid)
  const _userId = String(userId)

  if (userId && _authUserId !== _userId) {
    console.log('🤝 HTTP Unauthorized User')
    return res.status(401).json({
      error: true,
      data: 'Unauthorized User',
      message: 'O userId informado não corresponde ao usuário logado',
    })
  }

  const userDoc = await userById(_authUserId)
  const _authAccountId = userDoc?.accountId
  if (accountId && _authAccountId !== accountId) {
    console.log('🤝 Unauthorized User')
    return res.status(401).json({
      error: true,
      data: 'Unauthorized User',
      message: 'O accountId informado não pertence ao usuário logado',
    })
  }

  const { instanceId } = req.params
  if (instanceId) {
    const repo = new InstancesRepository()
    const instance = await repo.get(_authAccountId, instanceId)
    if (_authAccountId !== instance.data?.accountId) {
      console.log('🤝 Unauthorized User')
      return res.status(401).json({
        error: true,
        data: 'Unauthorized User',
        message: 'A instancia não pertence ao usuário',
      })
    }
  }

  console.log('🤝 HTTP Authorized User')
  req.accountId = _authAccountId
  return next()
}
